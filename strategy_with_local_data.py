#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用本地数据库的双线均值回归策略
"""

import backtrader as bt
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from local_data_manager import LocalDataManager
from data_config import config
from dual_ma_strategy import DualMAStrategy


class LocalDataFeed(bt.feeds.PandasData):
    """本地数据源"""
    
    params = (
        ('datetime', None),
        ('open', 'open'),
        ('high', 'high'),
        ('low', 'low'),
        ('close', 'close'),
        ('volume', 'volume'),
        ('openinterest', None),
    )


def run_backtest_with_local_data(symbol='AAPL', start_date='2023-01-01', end_date='2024-01-01', 
                                initial_cash=10000, commission=0.001, force_update=False):
    """使用本地数据运行回测"""
    
    print(f"=" * 60)
    print(f"本地数据回测: {symbol}")
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"初始资金: ${initial_cash:,.2f}")
    print("=" * 60)
    
    # 1. 初始化数据管理器
    print("1. 初始化数据管理器...")
    manager = LocalDataManager(
        db_path=config.get_database_path(),
        tushare_token=config.get_tushare_token()
    )
    
    # 2. 获取数据
    print("2. 获取股票数据...")
    data = manager.get_data(symbol, start_date, end_date, force_update=force_update)
    
    if data is None or data.empty:
        print("❌ 无法获取数据")
        return None
    
    print(f"✅ 获取到数据: {len(data)} 条记录")
    print(f"   数据范围: {data.index[0].date()} 到 {data.index[-1].date()}")
    
    # 3. 数据质量检查
    print("\n3. 数据质量检查...")
    
    # 检查缺失值
    missing_data = data.isnull().sum()
    if missing_data.sum() > 0:
        print(f"⚠️  发现缺失数据: {missing_data.to_dict()}")
        data = data.dropna()
        print(f"   清理后数据: {len(data)} 条记录")
    else:
        print("✅ 数据完整，无缺失值")
    
    # 检查价格合理性
    price_stats = data['close'].describe()
    print(f"   价格统计: 最低${price_stats['min']:.2f}, "
          f"最高${price_stats['max']:.2f}, "
          f"平均${price_stats['mean']:.2f}")
    
    if price_stats['min'] <= 0:
        print("❌ 发现异常价格数据")
        return None
    
    print("✅ 价格数据合理")
    
    # 4. 设置回测引擎
    print("\n4. 设置回测引擎...")
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(DualMAStrategy,
                       fast_period=10,
                       slow_period=30,
                       printlog=True)
    
    # 添加数据
    data_feed = LocalDataFeed(dataname=data)
    cerebro.adddata(data_feed)
    
    # 设置资金和手续费
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=commission)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
    
    # 5. 运行回测
    print("\n5. 运行回测...")
    print(f"初始资金: ${cerebro.broker.getvalue():,.2f}")
    
    results = cerebro.run()
    
    final_value = cerebro.broker.getvalue()
    total_return = final_value - initial_cash
    return_pct = (total_return / initial_cash) * 100
    
    print(f"最终资金: ${final_value:,.2f}")
    print(f"总收益: ${total_return:,.2f}")
    print(f"收益率: {return_pct:.2f}%")
    
    # 6. 分析结果
    print("\n6. 详细分析结果")
    print("=" * 40)
    
    strat = results[0]
    
    # 夏普比率
    try:
        sharpe = strat.analyzers.sharpe.get_analysis()
        if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:
            print(f"夏普比率: {sharpe['sharperatio']:.3f}")
        else:
            print("夏普比率: 无法计算")
    except:
        print("夏普比率: 计算出错")
    
    # 最大回撤
    try:
        drawdown = strat.analyzers.drawdown.get_analysis()
        max_dd = drawdown.get('max', {}).get('drawdown', 0)
        print(f"最大回撤: {max_dd:.2f}%")
    except:
        print("最大回撤: 计算出错")
    
    # 交易统计
    try:
        trades = strat.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)
        
        print(f"总交易次数: {total_trades}")
        print(f"盈利交易: {won_trades}")
        print(f"亏损交易: {lost_trades}")
        
        if total_trades > 0:
            win_rate = (won_trades / total_trades) * 100
            print(f"胜率: {win_rate:.2f}%")
            
            # 平均盈亏
            if won_trades > 0:
                avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
                print(f"平均盈利: ${avg_win:.2f}")
            
            if lost_trades > 0:
                avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
                print(f"平均亏损: ${avg_loss:.2f}")
    except:
        print("交易统计: 计算出错")
    
    # 7. 绘制图表
    print("\n7. 生成图表...")
    try:
        cerebro.plot(style='candlestick', barup='green', bardown='red')
        plt.show()
        print("✅ 图表已显示")
    except Exception as e:
        print(f"⚠️  图表生成失败: {e}")
    
    return results


def batch_backtest(symbols, start_date='2023-01-01', end_date='2024-01-01'):
    """批量回测多个股票"""
    print(f"=" * 60)
    print(f"批量回测 {len(symbols)} 个股票")
    print(f"时间范围: {start_date} 到 {end_date}")
    print("=" * 60)
    
    results = []
    
    for i, symbol in enumerate(symbols):
        print(f"\n回测进度: {i+1}/{len(symbols)} - {symbol}")
        print("-" * 40)
        
        try:
            result = run_backtest_with_local_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                initial_cash=10000,
                commission=0.001
            )
            
            if result:
                strat = result[0]
                
                # 提取关键指标
                final_value = strat.broker.getvalue()
                total_return = (final_value - 10000) / 10000 * 100
                
                drawdown = strat.analyzers.drawdown.get_analysis()
                max_dd = drawdown.get('max', {}).get('drawdown', 0)
                
                trades = strat.analyzers.trades.get_analysis()
                total_trades = trades.get('total', {}).get('total', 0)
                win_rate = 0
                if total_trades > 0:
                    won_trades = trades.get('won', {}).get('total', 0)
                    win_rate = (won_trades / total_trades) * 100
                
                results.append({
                    'symbol': symbol,
                    'return': total_return,
                    'max_drawdown': max_dd,
                    'total_trades': total_trades,
                    'win_rate': win_rate
                })
                
                print(f"✅ {symbol}: 收益率 {total_return:.2f}%, 最大回撤 {max_dd:.2f}%")
            else:
                print(f"❌ {symbol}: 回测失败")
                
        except Exception as e:
            print(f"❌ {symbol}: 错误 - {e}")
    
    # 总结结果
    if results:
        print(f"\n" + "=" * 60)
        print("批量回测结果总结")
        print("=" * 60)
        
        df = pd.DataFrame(results)
        
        print(f"成功回测: {len(results)}/{len(symbols)} 个股票")
        print(f"平均收益率: {df['return'].mean():.2f}%")
        print(f"平均最大回撤: {df['max_drawdown'].mean():.2f}%")
        print(f"平均胜率: {df['win_rate'].mean():.2f}%")
        
        # 最佳表现
        best_return = df.loc[df['return'].idxmax()]
        print(f"\n最佳收益: {best_return['symbol']} ({best_return['return']:.2f}%)")
        
        best_drawdown = df.loc[df['max_drawdown'].idxmin()]
        print(f"最小回撤: {best_drawdown['symbol']} ({best_drawdown['max_drawdown']:.2f}%)")
        
        # 保存结果
        df.to_csv('batch_backtest_results.csv', index=False)
        print(f"\n结果已保存到: batch_backtest_results.csv")
        
        return df
    
    return None


def main():
    """主函数"""
    print("本地数据库策略回测系统")
    print("请选择操作:")
    print("1. 单个股票回测")
    print("2. 批量股票回测")
    print("3. 数据库信息")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            symbol = input("请输入股票代码 (如 AAPL): ").strip().upper()
            start_date = input("开始日期 (YYYY-MM-DD, 回车使用默认): ").strip()
            end_date = input("结束日期 (YYYY-MM-DD, 回车使用默认): ").strip()
            
            if not start_date:
                start_date = '2023-01-01'
            if not end_date:
                end_date = '2024-01-01'
            
            run_backtest_with_local_data(symbol, start_date, end_date)
            
        elif choice == '2':
            symbols_input = input("请输入股票代码 (用逗号分隔, 如 AAPL,MSFT,GOOGL): ").strip()
            if symbols_input:
                symbols = [s.strip().upper() for s in symbols_input.split(',')]
            else:
                symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
            
            batch_backtest(symbols)
            
        elif choice == '3':
            manager = LocalDataManager(
                db_path=config.get_database_path(),
                tushare_token=config.get_tushare_token()
            )
            info = manager.get_database_info()
            
            print("\n数据库信息:")
            print(f"  股票数量: {info['symbol_count']}")
            print(f"  数据记录: {info['record_count']:,}")
            print(f"  数据范围: {info['date_range']}")
            print(f"  数据源统计: {info['source_stats']}")
            print(f"  数据库大小: {info['db_size']:.2f} MB")
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"错误: {e}")


if __name__ == '__main__':
    main()
