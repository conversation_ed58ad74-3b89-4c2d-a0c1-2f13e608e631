#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动测试真实数据获取 - 无需用户输入
"""

import sys
import time
from datetime import datetime, timedelta
from improved_data_fetcher import ImprovedDataFetcher


def main():
    """主测试函数"""
    print("=" * 60)
    print("自动测试真实数据获取")
    print("=" * 60)
    
    # 创建数据获取器
    fetcher = ImprovedDataFetcher()
    
    # 1. 测试连接
    print("\n1. 测试与Yahoo Finance的连接...")
    try:
        if fetcher.test_connection():
            print("✅ 连接测试通过")
        else:
            print("❌ 连接测试失败")
            return False
    except Exception as e:
        print(f"❌ 连接测试出错: {e}")
        return False
    
    # 2. 快速数据获取测试
    print("\n2. 快速数据获取测试...")
    
    # 计算最近30天的日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"获取时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    try:
        print("正在获取 AAPL 数据...")
        data = fetcher.get_stock_data(
            symbol='AAPL',
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        if data is not None and not data.empty:
            print(f"✅ 成功获取数据: {len(data)} 条记录")
            print(f"   数据范围: {data.index[0].date()} 到 {data.index[-1].date()}")
            print(f"   列名: {list(data.columns)}")
            
            if 'close' in data.columns:
                latest_price = data['close'].iloc[-1]
                print(f"   最新收盘价: ${latest_price:.2f}")
                
                # 显示最近5天的数据
                print("\n最近5天的数据:")
                recent_data = data.tail().round(2)
                for date, row in recent_data.iterrows():
                    print(f"   {date.date()}: 开盘${row['open']:.2f}, "
                          f"最高${row['high']:.2f}, 最低${row['low']:.2f}, "
                          f"收盘${row['close']:.2f}, 成交量{row['volume']:,}")
                
                # 验证数据质量
                if 50 <= latest_price <= 500:
                    print("✅ 价格数据看起来合理")
                    return True
                else:
                    print("⚠️  价格数据可能不准确")
            
        else:
            print("❌ 获取数据失败或为空")
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False


def test_multiple_symbols():
    """测试多个股票代码"""
    print("\n" + "=" * 60)
    print("测试多个股票代码")
    print("=" * 60)
    
    fetcher = ImprovedDataFetcher()
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    # 使用较短的时间范围以减少请求负担
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)  # 只获取最近一周的数据
    
    print(f"测试时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    success_count = 0
    
    for i, symbol in enumerate(symbols):
        print(f"\n测试 {symbol} ({i+1}/{len(symbols)})...")
        
        try:
            data = fetcher.get_stock_data(
                symbol=symbol,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if data is not None and not data.empty:
                latest_price = data['close'].iloc[-1] if 'close' in data.columns else 0
                print(f"✅ {symbol}: {len(data)} 条记录, 最新价格: ${latest_price:.2f}")
                success_count += 1
            else:
                print(f"❌ {symbol}: 数据获取失败")
                
        except Exception as e:
            print(f"❌ {symbol}: 获取出错 - {e}")
        
        # 在请求之间添加延迟
        if i < len(symbols) - 1:
            print("   等待3秒...")
            time.sleep(3)
    
    print(f"\n总结: {success_count}/{len(symbols)} 个股票数据获取成功")
    return success_count > 0


def run_strategy_test():
    """运行策略回测测试"""
    print("\n" + "=" * 60)
    print("运行策略回测测试")
    print("=" * 60)
    
    try:
        from dual_ma_strategy import run_backtest
        
        print("使用真实数据运行双线均值回归策略...")
        
        # 使用较短的时间范围
        result = run_backtest(
            symbol='AAPL',
            start_date='2023-11-01',
            end_date='2023-12-31',
            initial_cash=10000,
            commission=0.001
        )
        
        if result:
            print("✅ 策略回测完成")
            return True
        else:
            print("❌ 策略回测失败")
            
    except Exception as e:
        print(f"❌ 策略回测出错: {e}")
        import traceback
        traceback.print_exc()
    
    return False


if __name__ == '__main__':
    print("开始自动测试...")
    
    try:
        # 1. 基础连接和数据获取测试
        if main():
            print("\n✅ 基础测试通过")
            
            # 2. 多股票测试
            if test_multiple_symbols():
                print("\n✅ 多股票测试通过")
                
                # 3. 策略回测测试
                if run_strategy_test():
                    print("\n🎉 所有测试通过！真实数据获取功能正常工作")
                else:
                    print("\n⚠️  策略回测测试失败，但数据获取功能正常")
            else:
                print("\n⚠️  多股票测试失败，但基础功能正常")
        else:
            print("\n❌ 基础测试失败")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现未预期的错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n测试完成！")
