#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行双线均值回归策略的简单示例
"""

from dual_ma_strategy import run_backtest

def main():
    """主函数 - 运行不同的回测示例"""
    
    print("=" * 60)
    print("双线均值回归策略回测系统")
    print("=" * 60)
    
    # 示例1: 苹果公司股票
    print("\n示例1: 苹果公司 (AAPL) 2023年回测")
    run_backtest(
        symbol='AAPL',
        start_date='2023-01-01',
        end_date='2024-01-01',
        initial_cash=10000,
        commission=0.001
    )
    
    # 示例2: 微软公司股票
    print("\n\n示例2: 微软公司 (MSFT) 2023年回测")
    run_backtest(
        symbol='MSFT',
        start_date='2023-01-01',
        end_date='2024-01-01',
        initial_cash=10000,
        commission=0.001
    )
    
    # 示例3: 特斯拉股票 (更长时间周期)
    print("\n\n示例3: 特斯拉 (TSLA) 2022-2024年回测")
    run_backtest(
        symbol='TSLA',
        start_date='2022-01-01',
        end_date='2024-01-01',
        initial_cash=10000,
        commission=0.001
    )

if __name__ == '__main__':
    main()
