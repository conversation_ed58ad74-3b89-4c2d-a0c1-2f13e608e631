#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的环境管理助手
"""

import os
import subprocess
from pathlib import Path


def main():
    """主函数"""
    print("=" * 60)
    print("虚拟环境管理助手")
    print("=" * 60)
    
    project_root = Path.cwd()
    
    # 检查虚拟环境
    venv_path = project_root / 'venv'
    dot_venv_path = project_root / '.venv'
    
    print("发现的虚拟环境:")
    
    if venv_path.exists():
        print(f"✅ venv - 推荐使用 (包含所有依赖)")
        recommended = venv_path
    else:
        print("❌ venv - 不存在")
        recommended = None
    
    if dot_venv_path.exists():
        print(f"⚠️  .venv - 存在但几乎为空")
    else:
        print("❌ .venv - 不存在")
    
    if not recommended:
        print("\n❌ 未找到推荐的虚拟环境")
        print("请先创建并安装依赖:")
        print("  python -m venv venv")
        print("  source venv/bin/activate")
        print("  pip install pandas numpy matplotlib backtrader yfinance tushare")
        return
    
    print(f"\n🎯 推荐使用: {recommended.name}")
    
    # 创建便捷脚本
    print(f"\n" + "=" * 40)
    print("创建便捷脚本")
    print("=" * 40)
    
    # 激活脚本
    activate_script = f"""#!/bin/bash
source {recommended}/bin/activate
echo "✅ 虚拟环境已激活: {recommended.name}"
echo ""
echo "可用命令:"
echo "  python data_manager_cli.py download -s AAPL"
echo "  python strategy_with_local_data.py"
echo "  python demo_with_sample_data.py"
echo ""
echo "要退出虚拟环境，输入: deactivate"
exec bash
"""
    
    with open('activate_env.sh', 'w') as f:
        f.write(activate_script)
    os.chmod('activate_env.sh', 0o755)
    print("✅ 创建激活脚本: activate_env.sh")
    
    # 运行脚本
    scripts = {
        'run_download.sh': f"""#!/bin/bash
source {recommended}/bin/activate
python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01
""",
        'run_strategy.sh': f"""#!/bin/bash
source {recommended}/bin/activate
python strategy_with_local_data.py
""",
        'run_demo.sh': f"""#!/bin/bash
source {recommended}/bin/activate
python demo_with_sample_data.py
""",
        'run_query.sh': f"""#!/bin/bash
source {recommended}/bin/activate
python data_manager_cli.py query AAPL --start-date 2023-01-01
"""
    }
    
    for script_name, content in scripts.items():
        with open(script_name, 'w') as f:
            f.write(content)
        os.chmod(script_name, 0o755)
        print(f"✅ 创建运行脚本: {script_name}")
    
    # 使用说明
    print(f"\n" + "=" * 40)
    print("使用说明")
    print("=" * 40)
    
    print("方法1 - 手动激活环境:")
    print(f"  source {recommended}/bin/activate")
    print("  python data_manager_cli.py download -s AAPL")
    
    print("\n方法2 - 使用便捷脚本:")
    print("  ./activate_env.sh          # 激活环境")
    print("  ./run_download.sh          # 下载数据")
    print("  ./run_strategy.sh          # 运行策略")
    print("  ./run_demo.sh              # 运行演示")
    print("  ./run_query.sh             # 查询数据")
    
    print("\n方法3 - 一行命令:")
    print(f"  source {recommended}/bin/activate && python data_manager_cli.py download -s AAPL")
    
    # 环境清理建议
    if dot_venv_path.exists():
        print(f"\n" + "=" * 40)
        print("清理建议")
        print("=" * 40)
        print("发现空的 .venv 环境，建议删除以避免混淆:")
        print(f"  rm -rf {dot_venv_path}")
        print("⚠️  删除前请确认不再需要！")
    
    print(f"\n✅ 环境管理助手完成")


if __name__ == '__main__':
    main()
