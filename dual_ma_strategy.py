#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双线均值回归策略 - Backtrader实现
使用快速移动平均线和慢速移动平均线的交叉来判断买入卖出时机
"""

import backtrader as bt
import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from improved_data_fetcher import ImprovedDataFetcher


class DualMAStrategy(bt.Strategy):
    """
    双线均值回归策略
    
    策略逻辑：
    1. 当快线上穿慢线时买入
    2. 当快线下穿慢线时卖出
    3. 使用移动平均线的均值回归特性
    """
    
    params = (
        ('fast_period', 10),    # 快线周期
        ('slow_period', 30),    # 慢线周期
        ('printlog', True),     # 是否打印日志
    )
    
    def __init__(self):
        """初始化策略"""
        # 计算移动平均线
        self.fast_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.fast_period
        )
        self.slow_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.slow_period
        )
        
        # 计算交叉信号
        self.crossover = bt.indicators.CrossOver(self.fast_ma, self.slow_ma)
        
        # 记录订单状态
        self.order = None
        
        # 记录交易信息
        self.buyprice = None
        self.buycomm = None
        
    def log(self, txt, dt=None):
        """日志记录函数"""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}: {txt}')
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            # 订单已提交/已接受 - 无需处理
            return
        
        # 检查订单是否完成
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行: 价格 {order.executed.price:.2f}, '
                        f'数量 {order.executed.size}, '
                        f'手续费 {order.executed.comm:.2f}')
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            else:  # 卖出
                self.log(f'卖出执行: 价格 {order.executed.price:.2f}, '
                        f'数量 {order.executed.size}, '
                        f'手续费 {order.executed.comm:.2f}')
                
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单取消/保证金不足/拒绝')
        
        # 重置订单状态
        self.order = None
    
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
        
        self.log(f'交易盈亏: 毛利润 {trade.pnl:.2f}, 净利润 {trade.pnlcomm:.2f}')
    
    def next(self):
        """策略主逻辑"""
        # 记录当前价格和指标值
        self.log(f'收盘价: {self.data.close[0]:.2f}, '
                f'快线MA({self.params.fast_period}): {self.fast_ma[0]:.2f}, '
                f'慢线MA({self.params.slow_period}): {self.slow_ma[0]:.2f}')
        
        # 如果有未完成的订单，等待
        if self.order:
            return
        
        # 检查是否已持仓
        if not self.position:
            # 没有持仓，检查买入信号
            if self.crossover > 0:  # 快线上穿慢线
                self.log(f'买入信号: 快线上穿慢线')
                # 计算买入数量（使用95%的可用资金）
                size = int(self.broker.getcash() * 0.95 / self.data.close[0])
                self.order = self.buy(size=size)
                
        else:
            # 已持仓，检查卖出信号
            if self.crossover < 0:  # 快线下穿慢线
                self.log(f'卖出信号: 快线下穿慢线')
                self.order = self.sell(size=self.position.size)


def get_stock_data(symbol, start_date, end_date):
    """获取股票数据 - 使用改进的数据获取器"""
    fetcher = ImprovedDataFetcher()
    return fetcher.get_stock_data(symbol, start_date, end_date)


def generate_sample_data(start_date, end_date):
    """生成模拟股票数据用于演示"""
    import numpy as np

    # 创建日期范围
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    # 只保留工作日
    date_range = date_range[date_range.weekday < 5]

    # 生成模拟价格数据
    np.random.seed(42)  # 固定随机种子以获得可重复的结果

    n_days = len(date_range)
    initial_price = 150.0

    # 生成随机价格变化
    returns = np.random.normal(0.001, 0.02, n_days)  # 平均0.1%日收益，2%波动率

    # 添加一些趋势
    trend = np.sin(np.linspace(0, 4*np.pi, n_days)) * 0.005
    returns += trend

    # 计算累积价格
    prices = [initial_price]
    for i in range(1, n_days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(max(new_price, 1.0))  # 确保价格不为负

    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        # 生成开高低收价格
        daily_volatility = abs(np.random.normal(0, 0.01))
        high = price * (1 + daily_volatility)
        low = price * (1 - daily_volatility)

        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))

        close_price = price
        volume = int(np.random.normal(1000000, 200000))

        data.append({
            'open': open_price,
            'high': max(open_price, high, close_price),
            'low': min(open_price, low, close_price),
            'close': close_price,
            'volume': max(volume, 100000)
        })

    # 创建DataFrame
    df = pd.DataFrame(data, index=date_range)
    return df


def run_backtest(symbol='AAPL', start_date='2023-01-01', end_date='2024-01-01', 
                initial_cash=10000, commission=0.001):
    """运行回测"""
    
    print(f"开始回测 {symbol} 从 {start_date} 到 {end_date}")
    print(f"初始资金: ${initial_cash:,.2f}")
    print(f"手续费率: {commission*100:.3f}%")
    print("-" * 50)
    
    # 获取数据
    data = get_stock_data(symbol, start_date, end_date)
    if data is None:
        return
    
    # 创建Cerebro引擎
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(DualMAStrategy)
    
    # 添加数据
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # 设置初始资金
    cerebro.broker.setcash(initial_cash)
    
    # 设置手续费
    cerebro.broker.setcommission(commission=commission)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    
    # 记录初始资金
    print(f'初始资金: ${cerebro.broker.getvalue():,.2f}')
    
    # 运行回测
    results = cerebro.run()
    
    # 记录最终资金
    final_value = cerebro.broker.getvalue()
    print(f'最终资金: ${final_value:,.2f}')
    print(f'总收益: ${final_value - initial_cash:,.2f}')
    print(f'收益率: {((final_value - initial_cash) / initial_cash) * 100:.2f}%')
    
    # 打印分析结果
    strat = results[0]
    print("\n" + "="*50)
    print("回测结果分析")
    print("="*50)
    
    # 夏普比率
    if hasattr(strat.analyzers.sharpe, 'get_analysis'):
        sharpe = strat.analyzers.sharpe.get_analysis()
        if 'sharperatio' in sharpe:
            print(f"夏普比率: {sharpe['sharperatio']:.3f}")
    
    # 最大回撤
    drawdown = strat.analyzers.drawdown.get_analysis()
    print(f"最大回撤: {drawdown['max']['drawdown']:.2f}%")
    
    # 交易统计
    trades = strat.analyzers.trades.get_analysis()
    print(f"总交易次数: {trades.get('total', {}).get('total', 0)}")
    print(f"盈利交易: {trades.get('won', {}).get('total', 0)}")
    print(f"亏损交易: {trades.get('lost', {}).get('total', 0)}")
    
    if trades.get('won', {}).get('total', 0) > 0:
        win_rate = trades['won']['total'] / trades['total']['total'] * 100
        print(f"胜率: {win_rate:.2f}%")
    
    # 绘制结果
    cerebro.plot(style='candlestick', barup='green', bardown='red')
    plt.show()
    
    return results


if __name__ == '__main__':
    # 运行回测示例
    # 可以修改这些参数来测试不同的股票和时间段
    run_backtest(
        symbol='AAPL',           # 股票代码
        start_date='2023-01-01', # 开始日期
        end_date='2024-01-01',   # 结束日期
        initial_cash=10000,      # 初始资金
        commission=0.001         # 手续费率 (0.1%)
    )
