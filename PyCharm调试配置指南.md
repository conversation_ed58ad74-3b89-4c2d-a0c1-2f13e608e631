# PyCharm 调试配置指南

## 🎯 目标
在 PyCharm 中配置调试运行 `python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01`

## ✅ 已为你创建的配置文件

我已经在 `.idea/runConfigurations/` 目录下创建了以下配置文件：

1. **Download_Data.xml** - 下载数据配置
2. **Query_Data.xml** - 查询数据配置  
3. **Run_Strategy.xml** - 运行策略配置
4. **Database_Info.xml** - 数据库信息配置

重启 PyCharm 后，这些配置会自动出现在运行配置下拉菜单中。

## 🔧 手动配置步骤

### 步骤1: 打开运行配置
1. 点击菜单 `Run` → `Edit Configurations...`
2. 或点击右上角运行按钮旁的下拉菜单 → `Edit Configurations...`

### 步骤2: 添加新配置
1. 点击左上角的 `+` 号
2. 选择 `Python`

### 步骤3: 配置参数
```
Name: Download Data
Script path: data_manager_cli.py
Parameters: download -s AAPL,MSFT --start-date 2023-01-01
Python interpreter: venv (Python 3.12)
Working directory: /Users/<USER>/PycharmProjects/PythonProject1
Environment variables: (留空)
```

### 步骤4: 保存配置
点击 `OK` 保存配置

## 📋 推荐的多个配置

### 配置1: 下载数据
```
Name: Download AAPL,MSFT
Parameters: download -s AAPL,MSFT --start-date 2023-01-01
```

### 配置2: 下载单个股票
```
Name: Download AAPL
Parameters: download -s AAPL --start-date 2023-01-01 --force
```

### 配置3: 查询数据
```
Name: Query AAPL
Parameters: query AAPL --start-date 2023-01-01
```

### 配置4: 导出数据
```
Name: Export AAPL
Parameters: query AAPL --start-date 2023-01-01 --output aapl_data.csv
```

### 配置5: 数据库信息
```
Name: Database Info
Parameters: info --list-symbols
```

### 配置6: 批量更新
```
Name: Update All
Parameters: update --symbols AAPL,MSFT,GOOGL,TSLA
```

## 🐛 调试配置

### 设置断点
在以下关键位置设置断点：

1. **`data_manager_cli.py`**
   - 第 `args.func(args)` 行 (主函数调用)
   - `download_data` 函数开始处

2. **`local_data_manager.py`**
   - `fetch_and_store_data` 方法
   - `_fetch_from_yfinance` 方法
   - 错误处理部分

### 调试变量
在调试时关注这些变量：
- `args` - 命令行参数
- `symbol` - 当前处理的股票代码
- `data` - 获取到的数据
- `manager` - 数据管理器实例

## 🔍 调试技巧

### 1. 条件断点
右键断点 → `More` → 设置条件
```python
symbol == 'AAPL'  # 只在处理 AAPL 时停止
```

### 2. 日志断点
右键断点 → `More` → 勾选 `Log message to console`
```
Processing symbol: {symbol}
```

### 3. 异常断点
`Run` → `View Breakpoints` → `+` → `Python Exception Breakpoints`
添加 `requests.exceptions.RequestException` 来捕获网络错误

### 4. 监视表达式
在 `Variables` 面板右键 → `Add to Watches`
```python
len(data) if data is not None else 0
manager.get_database_info()
```

## 🛠️ 解释器配置

### 确保使用正确的解释器
1. `File` → `Settings` → `Project` → `Python Interpreter`
2. 确保选择的是 `venv` 环境
3. 路径应该是：`/Users/<USER>/PycharmProjects/PythonProject1/venv/bin/python`

### 如果解释器不正确
1. 点击齿轮图标 → `Add...`
2. 选择 `Existing environment`
3. 浏览到项目的 `venv/bin/python`

## 📊 运行和调试

### 正常运行
1. 选择配置：`Download Data`
2. 点击绿色运行按钮 ▶️
3. 或使用快捷键 `Shift + F10`

### 调试运行
1. 选择配置：`Download Data`
2. 点击绿色调试按钮 🐛
3. 或使用快捷键 `Shift + F9`

### 查看输出
- **Run** 窗口：查看程序输出
- **Debug** 窗口：查看调试信息
- **Console** 窗口：查看日志信息

## 🔧 常见问题解决

### 问题1: 找不到模块
**错误**: `ModuleNotFoundError: No module named 'pandas'`
**解决**: 检查 Python 解释器是否设置为 `venv`

### 问题2: 工作目录错误
**错误**: `FileNotFoundError: [Errno 2] No such file or directory`
**解决**: 确保 `Working directory` 设置为项目根目录

### 问题3: 参数解析错误
**错误**: `error: unrecognized arguments`
**解决**: 检查 `Parameters` 字段的参数格式

### 问题4: 权限错误
**错误**: `PermissionError`
**解决**: 确保对项目目录有写权限

## 📈 高级调试功能

### 1. 性能分析
`Run` → `Profile 'Download Data'`
查看函数调用时间和内存使用

### 2. 覆盖率分析
`Run` → `Run 'Download Data' with Coverage`
查看代码覆盖率

### 3. 远程调试
如果需要在服务器上调试，可以配置远程解释器

## 💡 最佳实践

1. **为每个主要功能创建单独的配置**
2. **使用有意义的配置名称**
3. **设置合适的断点位置**
4. **利用条件断点减少调试时间**
5. **使用监视表达式跟踪关键变量**
6. **定期检查解释器配置**

## 🎯 快速开始

1. 重启 PyCharm (加载新的配置文件)
2. 在右上角选择 `Download Data` 配置
3. 点击调试按钮 🐛
4. 在关键位置设置断点
5. 开始调试！

现在你可以在 PyCharm 中方便地调试数据管理系统了！
