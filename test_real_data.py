#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真实数据获取和回测
"""

import sys
import time
from datetime import datetime, timedelta
from improved_data_fetcher import ImprovedDataFetcher
from dual_ma_strategy import run_backtest, DualMAStrategy
import backtrader as bt
import matplotlib.pyplot as plt


def test_data_fetching():
    """测试数据获取功能"""
    print("=" * 60)
    print("测试数据获取功能")
    print("=" * 60)
    
    fetcher = ImprovedDataFetcher()
    
    # 1. 测试连接
    print("\n1. 测试与Yahoo Finance的连接...")
    if fetcher.test_connection():
        print("✅ 连接测试通过")
    else:
        print("❌ 连接测试失败")
        return False
    
    # 2. 测试获取单个股票数据
    print("\n2. 测试获取单个股票数据...")
    test_symbols = ['AAPL', 'MSFT', 'SPY']
    
    for symbol in test_symbols:
        print(f"\n测试获取 {symbol} 数据...")
        try:
            data = fetcher.get_stock_data(
                symbol=symbol,
                start_date='2023-11-01',  # 使用较短的时间范围
                end_date='2023-12-01'
            )
            
            if data is not None and not data.empty:
                print(f"✅ 成功获取 {symbol} 数据: {len(data)} 条记录")
                print(f"   数据范围: {data.index[0].date()} 到 {data.index[-1].date()}")
                print(f"   列名: {list(data.columns)}")
                print(f"   最新收盘价: ${data['close'].iloc[-1]:.2f}")
            else:
                print(f"❌ 获取 {symbol} 数据失败或为空")
                
        except Exception as e:
            print(f"❌ 获取 {symbol} 数据时出错: {e}")
        
        # 在请求之间添加延迟
        time.sleep(2)
    
    return True


def test_backtest_with_real_data():
    """使用真实数据进行回测测试"""
    print("\n" + "=" * 60)
    print("使用真实数据进行回测测试")
    print("=" * 60)
    
    # 测试参数
    test_configs = [
        {
            'symbol': 'AAPL',
            'start_date': '2023-10-01',
            'end_date': '2023-12-31',
            'name': '苹果公司 Q4 2023'
        },
        {
            'symbol': 'MSFT',
            'start_date': '2023-09-01',
            'end_date': '2023-12-31',
            'name': '微软公司 Q4 2023'
        },
        {
            'symbol': 'SPY',
            'start_date': '2023-06-01',
            'end_date': '2023-12-31',
            'name': 'S&P 500 ETF 下半年'
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n测试 {config['name']} ({config['symbol']})...")
        print("-" * 40)
        
        try:
            # 运行回测
            result = run_backtest(
                symbol=config['symbol'],
                start_date=config['start_date'],
                end_date=config['end_date'],
                initial_cash=10000,
                commission=0.001
            )
            
            if result:
                results.append({
                    'config': config,
                    'result': result
                })
                print(f"✅ {config['name']} 回测完成")
            else:
                print(f"❌ {config['name']} 回测失败")
                
        except Exception as e:
            print(f"❌ {config['name']} 回测出错: {e}")
        
        # 在回测之间添加延迟
        time.sleep(3)
    
    return results


def run_comprehensive_test():
    """运行综合测试"""
    print("开始综合测试...")
    
    # 1. 测试数据获取
    if not test_data_fetching():
        print("数据获取测试失败，停止测试")
        return
    
    # 2. 测试回测
    results = test_backtest_with_real_data()
    
    # 3. 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    if results:
        print(f"成功完成 {len(results)} 个回测:")
        for result in results:
            config = result['config']
            print(f"  ✅ {config['name']} ({config['symbol']})")
    else:
        print("❌ 没有成功完成的回测")
    
    print("\n测试完成！")


def quick_test():
    """快速测试 - 只测试一个股票的短期数据"""
    print("=" * 60)
    print("快速测试 - 获取AAPL最近一个月数据")
    print("=" * 60)
    
    fetcher = ImprovedDataFetcher()
    
    # 计算日期范围（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"获取时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    try:
        data = fetcher.get_stock_data(
            symbol='AAPL',
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        if data is not None and not data.empty:
            print(f"✅ 成功获取数据: {len(data)} 条记录")
            print("\n最近5天的数据:")
            print(data.tail().round(2))
            
            # 简单的数据验证
            if 'close' in data.columns:
                latest_price = data['close'].iloc[-1]
                print(f"\n最新收盘价: ${latest_price:.2f}")
                
                # 检查价格是否合理（AAPL通常在100-300之间）
                if 50 <= latest_price <= 500:
                    print("✅ 价格数据看起来合理")
                    return True
                else:
                    print("⚠️  价格数据可能不准确")
            
        else:
            print("❌ 获取数据失败或为空")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return False


def test_different_intervals():
    """测试不同的时间间隔"""
    print("=" * 60)
    print("测试不同时间间隔的数据获取")
    print("=" * 60)
    
    fetcher = ImprovedDataFetcher()
    intervals = ['1d', '1wk', '1mo']
    
    for interval in intervals:
        print(f"\n测试 {interval} 间隔数据...")
        try:
            data = fetcher.get_stock_data(
                symbol='AAPL',
                start_date='2023-01-01',
                end_date='2023-12-31',
                interval=interval
            )
            
            if data is not None and not data.empty:
                print(f"✅ {interval} 数据获取成功: {len(data)} 条记录")
            else:
                print(f"❌ {interval} 数据获取失败")
                
        except Exception as e:
            print(f"❌ {interval} 数据获取出错: {e}")
        
        time.sleep(1)


if __name__ == '__main__':
    print("真实数据获取测试程序")
    print("请选择测试模式:")
    print("1. 快速测试 (推荐)")
    print("2. 综合测试")
    print("3. 数据获取测试")
    print("4. 不同时间间隔测试")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            quick_test()
        elif choice == '2':
            run_comprehensive_test()
        elif choice == '3':
            test_data_fetching()
        elif choice == '4':
            test_different_intervals()
        else:
            print("无效选择，运行快速测试...")
            quick_test()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
