#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地数据库系统安装和设置脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def install_dependencies():
    """安装依赖包"""
    print("=" * 50)
    print("安装依赖包")
    print("=" * 50)
    
    # 基础依赖
    base_packages = [
        'pandas',
        'numpy', 
        'matplotlib',
        'backtrader',
        'yfinance'
    ]
    
    # 可选依赖
    optional_packages = [
        'tushare'  # 用于A股数据
    ]
    
    print("安装基础依赖包...")
    for package in base_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    print("\n安装可选依赖包...")
    for package in optional_packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"⚠️  {package} 安装失败 (可选包)")
    
    return True


def setup_database():
    """设置数据库"""
    print("\n" + "=" * 50)
    print("设置本地数据库")
    print("=" * 50)
    
    try:
        from local_data_manager import LocalDataManager
        from data_config import config
        
        # 初始化数据管理器
        manager = LocalDataManager(
            db_path=config.get_database_path(),
            tushare_token=config.get_tushare_token()
        )
        
        print("✅ 数据库初始化成功")
        
        # 显示数据库信息
        info = manager.get_database_info()
        print(f"数据库路径: {manager.db_path}")
        print(f"数据库大小: {info['db_size']:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库设置失败: {e}")
        return False


def download_sample_data():
    """下载示例数据"""
    print("\n" + "=" * 50)
    print("下载示例数据")
    print("=" * 50)
    
    try:
        from local_data_manager import LocalDataManager
        from data_config import config
        from datetime import datetime, timedelta
        
        manager = LocalDataManager(
            db_path=config.get_database_path(),
            tushare_token=config.get_tushare_token()
        )
        
        # 示例股票列表
        sample_symbols = ['AAPL', 'MSFT', 'GOOGL']
        
        # 时间范围：最近6个月
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
        
        print(f"下载时间范围: {start_date} 到 {end_date}")
        print(f"股票列表: {sample_symbols}")
        
        success_count = 0
        for symbol in sample_symbols:
            print(f"\n下载 {symbol} 数据...")
            try:
                data = manager.fetch_and_store_data(symbol, start_date, end_date)
                if data is not None and not data.empty:
                    print(f"✅ {symbol}: {len(data)} 条记录")
                    success_count += 1
                else:
                    print(f"❌ {symbol}: 下载失败")
            except Exception as e:
                print(f"❌ {symbol}: 错误 - {e}")
        
        print(f"\n示例数据下载完成: {success_count}/{len(sample_symbols)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 示例数据下载失败: {e}")
        return False


def run_test():
    """运行测试"""
    print("\n" + "=" * 50)
    print("运行系统测试")
    print("=" * 50)
    
    try:
        from strategy_with_local_data import run_backtest_with_local_data
        
        print("运行 AAPL 回测测试...")
        result = run_backtest_with_local_data(
            symbol='AAPL',
            start_date='2023-06-01',
            end_date='2023-12-31',
            initial_cash=10000
        )
        
        if result:
            print("✅ 系统测试通过")
            return True
        else:
            print("❌ 系统测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 系统测试失败: {e}")
        return False


def create_usage_guide():
    """创建使用指南"""
    guide_content = """
# 本地数据库股票回测系统使用指南

## 系统组件

1. **local_data_manager.py** - 核心数据管理器
2. **data_config.py** - 配置管理
3. **data_manager_cli.py** - 命令行工具
4. **strategy_with_local_data.py** - 策略回测

## 快速开始

### 1. 配置数据源
```bash
python data_config.py
```

### 2. 下载数据
```bash
# 下载单个股票
python data_manager_cli.py download -s AAPL --start-date 2023-01-01

# 下载多个股票
python data_manager_cli.py download -s AAPL,MSFT,GOOGL --start-date 2023-01-01
```

### 3. 查询数据
```bash
# 查询数据
python data_manager_cli.py query AAPL --start-date 2023-01-01

# 导出数据
python data_manager_cli.py query AAPL -o aapl_data.csv
```

### 4. 运行回测
```bash
python strategy_with_local_data.py
```

## 数据源配置

### Tushare (A股数据)
1. 注册 Tushare 账号: https://tushare.pro/
2. 获取 API Token
3. 运行配置向导设置 Token

### yfinance (美股数据)
- 默认启用，无需额外配置
- 支持美股、港股等市场

## 命令行工具

### 下载数据
```bash
python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01 --end-date 2023-12-31
```

### 查看数据库信息
```bash
python data_manager_cli.py info --list-symbols
```

### 批量更新
```bash
python data_manager_cli.py update --symbols AAPL,MSFT,GOOGL
```

## 策略回测

### 单个股票回测
```python
from strategy_with_local_data import run_backtest_with_local_data

result = run_backtest_with_local_data(
    symbol='AAPL',
    start_date='2023-01-01',
    end_date='2023-12-31',
    initial_cash=10000
)
```

### 批量回测
```python
from strategy_with_local_data import batch_backtest

results = batch_backtest(['AAPL', 'MSFT', 'GOOGL'])
```

## 数据库结构

- **stock_info**: 股票基本信息
- **daily_data**: 日线数据
- **update_log**: 更新日志

## 注意事项

1. 首次使用需要下载数据，可能需要较长时间
2. 建议定期更新数据以保持最新
3. Tushare 有访问频率限制，请合理使用
4. 数据库文件会随着数据增加而增大

## 故障排除

### 数据下载失败
- 检查网络连接
- 检查 API Token 是否正确
- 查看错误日志

### 回测失败
- 确保数据已下载
- 检查日期范围是否合理
- 查看数据质量

## 扩展功能

- 支持自定义策略
- 支持多种技术指标
- 支持参数优化
- 支持风险管理

更多信息请参考源代码注释。
"""
    
    with open('使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 使用指南已创建: 使用指南.md")


def main():
    """主安装流程"""
    print("本地数据库股票回测系统安装向导")
    print("=" * 60)
    
    steps = [
        ("安装依赖包", install_dependencies),
        ("设置数据库", setup_database),
        ("配置数据源", lambda: True),  # 跳过自动配置
        ("下载示例数据", download_sample_data),
        ("运行系统测试", run_test),
        ("创建使用指南", create_usage_guide)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n步骤: {step_name}")
        print("-" * 30)
        
        if step_name == "配置数据源":
            print("请稍后手动运行: python data_config.py")
            success_count += 1
            continue
        
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 出错: {e}")
    
    print(f"\n" + "=" * 60)
    print("安装完成")
    print("=" * 60)
    print(f"完成步骤: {success_count}/{len(steps)}")
    
    if success_count >= 4:
        print("✅ 系统安装成功！")
        print("\n下一步:")
        print("1. 运行配置向导: python data_config.py")
        print("2. 下载数据: python data_manager_cli.py download -s AAPL")
        print("3. 运行回测: python strategy_with_local_data.py")
    else:
        print("⚠️  安装过程中遇到问题，请检查错误信息")


if __name__ == '__main__':
    main()
