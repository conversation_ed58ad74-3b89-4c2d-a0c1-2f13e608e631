#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义双线均值回归策略参数示例
展示如何调整策略参数来优化性能
"""

import backtrader as bt
import yfinance as yf
from dual_ma_strategy import DualMAStrategy, get_stock_data


class CustomDualMAStrategy(DualMAStrategy):
    """
    自定义双线均值回归策略
    增加了止损和止盈功能
    """
    
    params = (
        ('fast_period', 10),     # 快线周期
        ('slow_period', 30),     # 慢线周期
        ('stop_loss', 0.05),     # 止损比例 (5%)
        ('take_profit', 0.15),   # 止盈比例 (15%)
        ('printlog', True),      # 是否打印日志
    )
    
    def __init__(self):
        """初始化策略"""
        super().__init__()
        
        # 添加止损止盈跟踪
        self.stop_price = None
        self.profit_price = None
    
    def next(self):
        """策略主逻辑 - 增加止损止盈"""
        # 记录当前价格和指标值
        current_price = self.data.close[0]
        
        self.log(f'收盘价: {current_price:.2f}, '
                f'快线MA({self.params.fast_period}): {self.fast_ma[0]:.2f}, '
                f'慢线MA({self.params.slow_period}): {self.slow_ma[0]:.2f}')
        
        # 如果有未完成的订单，等待
        if self.order:
            return
        
        # 检查是否已持仓
        if not self.position:
            # 没有持仓，检查买入信号
            if self.crossover > 0:  # 快线上穿慢线
                self.log(f'买入信号: 快线上穿慢线')
                # 计算买入数量（使用95%的可用资金）
                size = int(self.broker.getcash() * 0.95 / current_price)
                self.order = self.buy(size=size)
                
                # 设置止损止盈价格
                self.stop_price = current_price * (1 - self.params.stop_loss)
                self.profit_price = current_price * (1 + self.params.take_profit)
                
                self.log(f'设置止损价格: {self.stop_price:.2f}')
                self.log(f'设置止盈价格: {self.profit_price:.2f}')
                
        else:
            # 已持仓，检查卖出信号
            sell_signal = False
            sell_reason = ""
            
            # 检查止损
            if current_price <= self.stop_price:
                sell_signal = True
                sell_reason = f"止损触发 (价格: {current_price:.2f} <= 止损: {self.stop_price:.2f})"
            
            # 检查止盈
            elif current_price >= self.profit_price:
                sell_signal = True
                sell_reason = f"止盈触发 (价格: {current_price:.2f} >= 止盈: {self.profit_price:.2f})"
            
            # 检查技术信号
            elif self.crossover < 0:  # 快线下穿慢线
                sell_signal = True
                sell_reason = "技术信号: 快线下穿慢线"
            
            if sell_signal:
                self.log(f'卖出信号: {sell_reason}')
                self.order = self.sell(size=self.position.size)
                # 重置止损止盈价格
                self.stop_price = None
                self.profit_price = None


def run_custom_backtest(symbol='AAPL', start_date='2023-01-01', end_date='2024-01-01'):
    """运行自定义策略回测"""
    
    print(f"运行自定义策略回测: {symbol}")
    print("-" * 50)
    
    # 获取数据
    data = get_stock_data(symbol, start_date, end_date)
    if data is None:
        return
    
    # 创建Cerebro引擎
    cerebro = bt.Cerebro()
    
    # 添加自定义策略
    cerebro.addstrategy(CustomDualMAStrategy,
                       fast_period=5,      # 更快的快线
                       slow_period=20,     # 更快的慢线
                       stop_loss=0.03,     # 3% 止损
                       take_profit=0.10)   # 10% 止盈
    
    # 添加数据
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # 设置初始资金和手续费
    cerebro.broker.setcash(10000)
    cerebro.broker.setcommission(commission=0.001)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    
    # 运行回测
    print(f'初始资金: ${cerebro.broker.getvalue():,.2f}')
    results = cerebro.run()
    final_value = cerebro.broker.getvalue()
    
    print(f'最终资金: ${final_value:,.2f}')
    print(f'总收益: ${final_value - 10000:,.2f}')
    print(f'收益率: {((final_value - 10000) / 10000) * 100:.2f}%')
    
    # 分析结果
    strat = results[0]
    drawdown = strat.analyzers.drawdown.get_analysis()
    trades = strat.analyzers.trades.get_analysis()
    
    print(f"最大回撤: {drawdown['max']['drawdown']:.2f}%")
    print(f"总交易次数: {trades.get('total', {}).get('total', 0)}")
    
    if trades.get('total', {}).get('total', 0) > 0:
        win_rate = trades.get('won', {}).get('total', 0) / trades['total']['total'] * 100
        print(f"胜率: {win_rate:.2f}%")
    
    # 绘制结果
    cerebro.plot()
    
    return results


def compare_strategies(symbol='AAPL'):
    """比较不同策略参数的效果"""
    
    print("=" * 60)
    print(f"策略参数比较 - {symbol}")
    print("=" * 60)
    
    # 获取数据
    data = get_stock_data(symbol, '2023-01-01', '2024-01-01')
    if data is None:
        return
    
    # 测试不同的参数组合
    param_combinations = [
        {'fast': 5, 'slow': 15, 'name': '快速策略 (5/15)'},
        {'fast': 10, 'slow': 30, 'name': '标准策略 (10/30)'},
        {'fast': 20, 'slow': 50, 'name': '慢速策略 (20/50)'},
    ]
    
    results = []
    
    for params in param_combinations:
        print(f"\n测试 {params['name']}:")
        
        cerebro = bt.Cerebro()
        cerebro.addstrategy(DualMAStrategy,
                           fast_period=params['fast'],
                           slow_period=params['slow'],
                           printlog=False)  # 关闭详细日志
        
        data_feed = bt.feeds.PandasData(dataname=data)
        cerebro.adddata(data_feed)
        cerebro.broker.setcash(10000)
        cerebro.broker.setcommission(commission=0.001)
        
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        initial_value = cerebro.broker.getvalue()
        result = cerebro.run()
        final_value = cerebro.broker.getvalue()
        
        strat = result[0]
        returns = strat.analyzers.returns.get_analysis()
        drawdown = strat.analyzers.drawdown.get_analysis()
        trades = strat.analyzers.trades.get_analysis()
        
        total_return = (final_value - initial_value) / initial_value * 100
        max_drawdown = drawdown['max']['drawdown']
        total_trades = trades.get('total', {}).get('total', 0)
        win_rate = 0
        if total_trades > 0:
            win_rate = trades.get('won', {}).get('total', 0) / total_trades * 100
        
        results.append({
            'name': params['name'],
            'return': total_return,
            'drawdown': max_drawdown,
            'trades': total_trades,
            'win_rate': win_rate
        })
        
        print(f"  总收益率: {total_return:.2f}%")
        print(f"  最大回撤: {max_drawdown:.2f}%")
        print(f"  交易次数: {total_trades}")
        print(f"  胜率: {win_rate:.2f}%")
    
    # 总结比较结果
    print("\n" + "=" * 60)
    print("策略比较总结")
    print("=" * 60)
    
    best_return = max(results, key=lambda x: x['return'])
    best_drawdown = min(results, key=lambda x: x['drawdown'])
    best_winrate = max(results, key=lambda x: x['win_rate'])
    
    print(f"最佳收益策略: {best_return['name']} ({best_return['return']:.2f}%)")
    print(f"最小回撤策略: {best_drawdown['name']} ({best_drawdown['drawdown']:.2f}%)")
    print(f"最高胜率策略: {best_winrate['name']} ({best_winrate['win_rate']:.2f}%)")


if __name__ == '__main__':
    # 运行自定义策略示例
    run_custom_backtest('AAPL', '2023-01-01', '2024-01-01')
    
    # 比较不同策略参数
    compare_strategies('AAPL')
