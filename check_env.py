#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境状态检查工具
"""

import os
import sys
import subprocess
from pathlib import Path


def check_virtual_env():
    """检查虚拟环境状态"""
    print("=" * 50)
    print("虚拟环境状态检查")
    print("=" * 50)
    
    # 检查 VIRTUAL_ENV 环境变量
    virtual_env = os.environ.get('VIRTUAL_ENV')
    if virtual_env:
        print(f"✅ 当前虚拟环境: {virtual_env}")
        print(f"   环境名称: {Path(virtual_env).name}")
    else:
        print("❌ 未激活虚拟环境")
    
    # 检查 Python 路径
    python_path = sys.executable
    print(f"🐍 Python 路径: {python_path}")
    
    # 检查是否在项目虚拟环境中
    project_venv = Path.cwd() / 'venv'
    if virtual_env and Path(virtual_env) == project_venv:
        print("✅ 正在使用项目虚拟环境")
    elif virtual_env:
        print("⚠️  正在使用其他虚拟环境")
    else:
        print("❌ 未使用虚拟环境")
    
    return virtual_env


def check_packages():
    """检查关键包是否可用"""
    print(f"\n" + "=" * 50)
    print("关键包检查")
    print("=" * 50)
    
    packages = [
        'pandas',
        'numpy', 
        'matplotlib',
        'backtrader',
        'yfinance',
        'tushare'
    ]
    
    available_packages = []
    missing_packages = []
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            available_packages.append(package)
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    print(f"\n📊 统计:")
    print(f"   可用包: {len(available_packages)}/{len(packages)}")
    print(f"   缺失包: {missing_packages}")
    
    return len(missing_packages) == 0


def check_prompt():
    """检查命令提示符"""
    print(f"\n" + "=" * 50)
    print("命令提示符检查")
    print("=" * 50)
    
    # 检查 PS1 环境变量
    ps1 = os.environ.get('PS1', '')
    if '((' in ps1 and '))' in ps1:
        print("⚠️  检测到双括号提示符问题")
        print("   这通常是因为重复激活虚拟环境")
        print("   解决方法: 执行 deactivate 然后重新激活")
    elif '(' in ps1 and ')' in ps1:
        print("✅ 正常的虚拟环境提示符")
    else:
        print("ℹ️  未检测到虚拟环境提示符")
    
    print(f"   当前 PS1: {ps1[:100]}...")


def provide_solutions():
    """提供解决方案"""
    print(f"\n" + "=" * 50)
    print("解决方案")
    print("=" * 50)
    
    virtual_env = os.environ.get('VIRTUAL_ENV')
    project_venv = Path.cwd() / 'venv'
    
    if not virtual_env:
        print("🔧 激活虚拟环境:")
        print("   source venv/bin/activate")
        print("   # 或")
        print("   source activate_simple.sh")
    
    elif Path(virtual_env) != project_venv:
        print("🔧 切换到项目虚拟环境:")
        print("   deactivate")
        print("   source venv/bin/activate")
    
    else:
        print("✅ 环境配置正确")
    
    print(f"\n🛠️  如果出现双括号问题:")
    print("   1. 执行 deactivate (可能需要多次)")
    print("   2. 重新激活: source venv/bin/activate")
    print("   3. 避免重复激活环境")
    
    print(f"\n📋 推荐的使用方式:")
    print("   # 方法1: 直接激活")
    print("   source venv/bin/activate")
    print("   python data_manager_cli.py download -s AAPL")
    print("")
    print("   # 方法2: 一行命令")
    print("   source venv/bin/activate && python data_manager_cli.py download -s AAPL")
    print("")
    print("   # 方法3: 使用便捷脚本")
    print("   ./run_download.sh")


def main():
    """主函数"""
    print("环境诊断工具")
    
    # 检查虚拟环境
    virtual_env = check_virtual_env()
    
    # 检查包
    packages_ok = check_packages()
    
    # 检查提示符
    check_prompt()
    
    # 提供解决方案
    provide_solutions()
    
    # 总结
    print(f"\n" + "=" * 50)
    print("诊断总结")
    print("=" * 50)
    
    if virtual_env and packages_ok:
        print("🎉 环境配置完美！")
    elif virtual_env:
        print("⚠️  虚拟环境已激活，但缺少一些包")
    else:
        print("❌ 需要激活虚拟环境")
    
    print(f"\n💡 如果遇到双括号问题 ((venv)):")
    print("   这是重复激活导致的，执行 deactivate 后重新激活即可")


if __name__ == '__main__':
    main()
