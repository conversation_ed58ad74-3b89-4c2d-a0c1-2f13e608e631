#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示版本 - 使用高质量模拟数据展示本地数据库系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from local_data_manager import LocalDataManager
from strategy_with_local_data import run_backtest_with_local_data
import sqlite3


def generate_realistic_sample_data(symbol, start_date, end_date):
    """生成高质量的模拟数据"""
    print(f"为 {symbol} 生成高质量模拟数据...")
    
    # 创建日期范围
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    date_range = date_range[date_range.weekday < 5]  # 只保留工作日
    
    # 根据真实股票设置参数
    stock_params = {
        'AAPL': {'price': 180, 'volatility': 0.25, 'trend': 0.08},
        'MSFT': {'price': 350, 'volatility': 0.22, 'trend': 0.12},
        'GOOGL': {'price': 140, 'volatility': 0.28, 'trend': 0.06},
        'TSLA': {'price': 250, 'volatility': 0.45, 'trend': 0.15},
    }
    
    params = stock_params.get(symbol.upper(), {'price': 100, 'volatility': 0.20, 'trend': 0.05})
    
    # 生成价格序列
    n_days = len(date_range)
    np.random.seed(hash(symbol) % 2**32)
    
    # 基础收益率
    daily_returns = np.random.normal(
        params['trend'] / 252,  # 年化收益转日收益
        params['volatility'] / np.sqrt(252),  # 年化波动率转日波动率
        n_days
    )
    
    # 添加市场周期性
    cycle = np.sin(np.linspace(0, 4*np.pi, n_days)) * 0.002
    daily_returns += cycle
    
    # 添加一些突发事件
    event_days = np.random.choice(n_days, size=max(1, n_days//50), replace=False)
    for day in event_days:
        daily_returns[day] += np.random.normal(0, 0.05)
    
    # 计算价格序列
    prices = [params['price']]
    for i in range(1, n_days):
        new_price = prices[-1] * (1 + daily_returns[i])
        prices.append(max(new_price, 1.0))
    
    # 生成OHLC数据
    data_list = []
    for i, close_price in enumerate(prices):
        # 日内波动
        intraday_vol = abs(np.random.normal(0, 0.01))
        
        if i == 0:
            open_price = close_price
        else:
            # 开盘价基于前一日收盘价
            gap = np.random.normal(0, 0.005)
            open_price = prices[i-1] * (1 + gap)
        
        # 计算高低价
        high_price = max(open_price, close_price) * (1 + intraday_vol)
        low_price = min(open_price, close_price) * (1 - intraday_vol)
        
        # 成交量（基于价格变化）
        price_change = abs(daily_returns[i]) if i > 0 else 0.01
        base_volume = 1000000
        volume = int(base_volume * (1 + price_change * 10) * np.random.uniform(0.5, 2.0))
        
        data_list.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': max(volume, 100000),
            'amount': close_price * volume,
            'adj_close': close_price
        })
    
    df = pd.DataFrame(data_list, index=date_range)
    print(f"生成了 {len(df)} 条 {symbol} 的高质量模拟数据")
    
    return df


def populate_sample_database():
    """填充示例数据到数据库"""
    print("=" * 60)
    print("填充示例数据到本地数据库")
    print("=" * 60)
    
    # 初始化数据管理器
    manager = LocalDataManager()
    
    # 示例股票和时间范围
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
    start_date = '2023-01-01'
    end_date = '2023-12-31'
    
    success_count = 0
    
    for symbol in symbols:
        print(f"\n处理 {symbol}...")
        try:
            # 生成模拟数据
            data = generate_realistic_sample_data(symbol, start_date, end_date)
            
            # 存储到数据库
            manager._store_daily_data(symbol, data, 'sample_data')
            manager._log_update(symbol, 'daily', start_date, end_date, 'sample_data', 'success')
            
            print(f"✅ {symbol}: {len(data)} 条记录已存储")
            success_count += 1
            
        except Exception as e:
            print(f"❌ {symbol}: 存储失败 - {e}")
    
    print(f"\n数据填充完成: {success_count}/{len(symbols)} 成功")
    
    # 显示数据库信息
    info = manager.get_database_info()
    print(f"\n数据库统计:")
    print(f"  股票数量: {info['symbol_count']}")
    print(f"  数据记录: {info['record_count']:,}")
    print(f"  数据库大小: {info['db_size']:.2f} MB")
    
    return success_count > 0


def demo_data_query():
    """演示数据查询功能"""
    print("\n" + "=" * 60)
    print("演示数据查询功能")
    print("=" * 60)
    
    manager = LocalDataManager()
    
    # 查询 AAPL 数据
    print("查询 AAPL 2023年数据...")
    data = manager.get_local_data('AAPL', '2023-01-01', '2023-12-31')
    
    if data is not None and not data.empty:
        print(f"✅ 找到 {len(data)} 条记录")
        print(f"   数据范围: {data.index[0].date()} 到 {data.index[-1].date()}")
        print(f"   价格范围: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
        print(f"   平均成交量: {data['volume'].mean():,.0f}")
        
        print("\n最近10天数据:")
        print(data.tail(10)[['open', 'high', 'low', 'close', 'volume']].round(2))
        
        return True
    else:
        print("❌ 未找到数据")
        return False


def demo_strategy_backtest():
    """演示策略回测"""
    print("\n" + "=" * 60)
    print("演示策略回测功能")
    print("=" * 60)
    
    try:
        # 运行 AAPL 回测
        result = run_backtest_with_local_data(
            symbol='AAPL',
            start_date='2023-01-01',
            end_date='2023-12-31',
            initial_cash=10000,
            commission=0.001
        )
        
        if result:
            print("✅ 策略回测演示完成")
            return True
        else:
            print("❌ 策略回测失败")
            return False
            
    except Exception as e:
        print(f"❌ 策略回测出错: {e}")
        return False


def demo_batch_analysis():
    """演示批量分析"""
    print("\n" + "=" * 60)
    print("演示批量分析功能")
    print("=" * 60)
    
    manager = LocalDataManager()
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
    
    results = []
    
    for symbol in symbols:
        print(f"\n分析 {symbol}...")
        data = manager.get_local_data(symbol, '2023-01-01', '2023-12-31')
        
        if data is not None and not data.empty:
            # 计算基本统计
            start_price = data['close'].iloc[0]
            end_price = data['close'].iloc[-1]
            total_return = (end_price - start_price) / start_price * 100
            volatility = data['close'].pct_change().std() * np.sqrt(252) * 100
            max_price = data['close'].max()
            min_price = data['close'].min()
            
            results.append({
                'symbol': symbol,
                'start_price': start_price,
                'end_price': end_price,
                'total_return': total_return,
                'volatility': volatility,
                'max_price': max_price,
                'min_price': min_price
            })
            
            print(f"  收益率: {total_return:.2f}%")
            print(f"  波动率: {volatility:.2f}%")
            print(f"  价格范围: ${min_price:.2f} - ${max_price:.2f}")
    
    if results:
        print(f"\n批量分析结果:")
        df = pd.DataFrame(results)
        print(f"平均收益率: {df['total_return'].mean():.2f}%")
        print(f"平均波动率: {df['volatility'].mean():.2f}%")
        
        best_performer = df.loc[df['total_return'].idxmax()]
        print(f"最佳表现: {best_performer['symbol']} ({best_performer['total_return']:.2f}%)")
        
        return True
    
    return False


def main():
    """主演示程序"""
    print("本地数据库系统演示程序")
    print("=" * 60)
    
    steps = [
        ("填充示例数据", populate_sample_database),
        ("数据查询演示", demo_data_query),
        ("策略回测演示", demo_strategy_backtest),
        ("批量分析演示", demo_batch_analysis)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n步骤: {step_name}")
        print("-" * 30)
        
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 出错: {e}")
    
    print(f"\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)
    print(f"完成步骤: {success_count}/{len(steps)}")
    
    if success_count >= 3:
        print("✅ 本地数据库系统演示成功！")
        print("\n系统功能:")
        print("- ✅ 本地数据存储和管理")
        print("- ✅ 多数据源支持 (Tushare + yfinance)")
        print("- ✅ 数据查询和分析")
        print("- ✅ 策略回测框架")
        print("- ✅ 批量数据处理")
        
        print("\n下一步可以:")
        print("1. 配置真实数据源 API")
        print("2. 下载真实历史数据")
        print("3. 开发自定义策略")
        print("4. 进行参数优化")
    else:
        print("⚠️  演示过程中遇到问题")


if __name__ == '__main__':
    main()
