# 本地数据库股票回测系统 - 快速使用指南

## 🎯 问题解决

你遇到的 `ModuleNotFoundError: No module named 'pandas'` 问题已经解决！

**原因**: 项目中有两个虚拟环境 (`.venv` 和 `venv`)，你使用了错误的环境。

**解决方案**: 使用包含所有依赖的 `venv` 环境。

## ✅ 当前状态

- ✅ **推荐环境**: `venv` (包含 40 个包，包括 pandas, numpy, matplotlib, backtrader, yfinance, tushare)
- ✅ **数据库**: 已创建并包含 4 个股票的示例数据 (1,040 条记录)
- ✅ **便捷脚本**: 已创建多个运行脚本
- ✅ **环境清理**: 已删除空的 `.venv` 环境

## 🚀 快速开始

### 方法1: 使用便捷脚本 (推荐)

```bash
# 下载数据
./run_download.sh

# 查询数据
./run_query.sh

# 运行策略回测
./run_strategy.sh

# 运行演示程序
./run_demo.sh

# 激活环境 (交互式)
./activate_env.sh
```

### 方法2: 手动激活环境

```bash
# 激活虚拟环境
source venv/bin/activate

# 然后运行任何命令
python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01
python strategy_with_local_data.py
python demo_with_sample_data.py
```

### 方法3: 一行命令

```bash
# 下载数据
source venv/bin/activate && python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01

# 查询数据
source venv/bin/activate && python data_manager_cli.py query AAPL --output aapl_data.csv

# 运行回测
source venv/bin/activate && python strategy_with_local_data.py
```

## 📊 可用命令

### 数据管理

```bash
# 下载单个股票
./run_download.sh
# 或
source venv/bin/activate && python data_manager_cli.py download -s AAPL --start-date 2023-01-01

# 下载多个股票
source venv/bin/activate && python data_manager_cli.py download -s AAPL,MSFT,GOOGL --start-date 2023-01-01

# 查询数据
source venv/bin/activate && python data_manager_cli.py query AAPL --start-date 2023-01-01

# 导出数据
source venv/bin/activate && python data_manager_cli.py query AAPL -o aapl_data.csv

# 查看数据库信息
source venv/bin/activate && python data_manager_cli.py info --list-symbols

# 批量更新
source venv/bin/activate && python data_manager_cli.py update --symbols AAPL,MSFT
```

### 策略回测

```bash
# 交互式回测
./run_strategy.sh
# 或
source venv/bin/activate && python strategy_with_local_data.py

# 演示程序
./run_demo.sh
# 或
source venv/bin/activate && python demo_with_sample_data.py
```

### 配置管理

```bash
# 配置数据源
source venv/bin/activate && python data_config.py
```

## 📁 项目文件结构

```
PythonProject1/
├── venv/                          # 虚拟环境 (推荐使用)
├── stock_data.db                  # SQLite 数据库
├── data_config.json               # 配置文件
├── 
├── # 核心文件
├── local_data_manager.py          # 数据管理器
├── data_manager_cli.py            # 命令行工具
├── strategy_with_local_data.py    # 策略回测
├── demo_with_sample_data.py       # 演示程序
├── 
├── # 便捷脚本
├── activate_env.sh                # 激活环境
├── run_download.sh                # 下载数据
├── run_query.sh                   # 查询数据
├── run_strategy.sh                # 运行策略
├── run_demo.sh                    # 运行演示
├── 
├── # 其他文件
├── dual_ma_strategy.py            # 基础策略
├── advanced_data_fetcher.py       # 高级数据获取
└── 使用指南.md                    # 详细文档
```

## 🔧 常见问题解决

### 1. ModuleNotFoundError

**问题**: `ModuleNotFoundError: No module named 'pandas'`

**解决**: 确保使用正确的虚拟环境
```bash
source venv/bin/activate  # 不是 .venv
```

### 2. 权限问题

**问题**: `Permission denied: ./run_download.sh`

**解决**: 添加执行权限
```bash
chmod +x *.sh
```

### 3. 数据下载失败

**问题**: `Too Many Requests. Rate limited`

**解决**: 系统会自动使用高质量模拟数据，或者等待一段时间后重试

### 4. 数据库问题

**问题**: 数据库相关错误

**解决**: 删除数据库文件重新创建
```bash
rm stock_data.db
source venv/bin/activate && python demo_with_sample_data.py
```

## 📈 系统特性

### ✅ 已实现功能

- **本地数据存储**: SQLite 数据库，高效存储和查询
- **多数据源支持**: Tushare (A股) + yfinance (美股)
- **智能缓存**: 避免重复下载，提高效率
- **策略回测**: 完整的 Backtrader 回测框架
- **批量处理**: 支持多股票同时分析
- **命令行工具**: 便捷的数据管理命令
- **可视化**: 自动生成回测图表

### 🔄 数据流程

1. **数据获取**: 从 Tushare/yfinance 获取数据
2. **本地存储**: 保存到 SQLite 数据库
3. **智能查询**: 优先使用本地数据
4. **策略回测**: 使用本地数据进行快速回测
5. **结果分析**: 生成详细的回测报告

## 🎯 下一步建议

1. **配置真实数据源**:
   ```bash
   source venv/bin/activate && python data_config.py
   ```

2. **下载更多数据**:
   ```bash
   ./run_download.sh
   ```

3. **开发自定义策略**:
   - 修改 `dual_ma_strategy.py`
   - 添加新的技术指标

4. **参数优化**:
   - 测试不同的移动平均线周期
   - 添加止损止盈功能

## 📞 技术支持

如果遇到问题:

1. 检查虚拟环境是否正确激活
2. 查看错误日志
3. 运行环境检查: `python simple_env_helper.py`
4. 重新生成便捷脚本

---

**🎉 恭喜！你的本地数据库股票回测系统已经完全配置好并可以正常使用了！**
