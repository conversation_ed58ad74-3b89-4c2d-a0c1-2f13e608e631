# -*- coding:utf-8 -*- 
import sys
PY3 = (sys.version_info[0] >= 3)
HTTP_OK = 200
HTTP_AUTHORIZATION_ERROR = 401
HTTP_URL = 'api.wmcloud.com'
HTTP_PORT = 443
BOND = '/api/bond/getBond.csv?secID=%s&ticker=%s&field=%s'
BONDCF = '/api/bond/getBondCf.csv?secID=%s&ticker=%s&beginDate=%s&cashTypeCD=%s&endDate=%s&field=%s'
BONDCOUPON = '/api/bond/getBondCoupon.csv?secID=%s&ticker=%s&field=%s'
BONDGUAR = '/api/bond/getBondGuar.csv?secID=%s&ticker=%s&guarModeCD=%s&field=%s'
BONDISSUE = '/api/bond/getBondIssue.csv?secID=%s&ticker=%s&raiseModeCD=%s&field=%s'
BONDOPTION = '/api/bond/getBondOption.csv?secID=%s&ticker=%s&field=%s'
BONDRATING = '/api/bond/getBondRating.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
EQU = '/api/equity/getEqu.csv?equTypeCD=%s&secID=%s&ticker=%s&listStatusCD=%s&field=%s'
EQUALLOT = '/api/equity/getEquAllot.csv?isAllotment=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
EQUDIV = '/api/equity/getEquDiv.csv?eventProcessCD=%s&exDivDate=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
EQUINDUSTRY = '/api/equity/getEquIndustry.csv?industry=%s&industryID=%s&industryVersionCD=%s&secID=%s&ticker=%s&intoDate=%s&field=%s'
EQUIPO = '/api/equity/getEquIPO.csv?eventProcessCD=%s&secID=%s&ticker=%s&field=%s'
EQUREF = '/api/equity/getEquRef.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&eventProcessCD=%s&field=%s'
EQURETUD = '/api/equity/getEquRetud.csv?listStatusCD=%s&secID=%s&ticker=%s&beginDate=%s&dailyReturnNoReinvLower=%s&dailyReturnNoReinvUpper=%s&dailyReturnReinvLower=%s&dailyReturnReinvUpper=%s&endDate=%s&isChgPctl=%s&field=%s'
EQUSPLITS = '/api/equity/getEquSplits.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUTU = '/api/future/getFutu.csv?exchangeCD=%s&secID=%s&ticker=%s&contractObject=%s&field=%s'
FUTUCONVF = '/api/future/getFutuConvf.csv?secID=%s&ticker=%s&field=%s'
GUARRATING = '/api/bond/getGuarRating.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
IDX = '/api/idx/getIdx.csv?secID=%s&ticker=%s&field=%s'
IDXCONS = '/api/idx/getIdxCons.csv?secID=%s&ticker=%s&intoDate=%s&intoDate=%s&isNew=%s&field=%s'
IDXWEIGHT = '/api/idx/getIdxCloseWeight.json?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
ISSUERRATING = '/api/bond/getIssuerRating.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTEQUD = '/api/market/getMktEqud.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
MKTFUTD = '/api/market/getMktFutd.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
MKTIDXD = '/api/market/getMktIdxd.csv?indexID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
SECID = '/api/master/getSecID.csv?assetClass=%s&cnSpell=%s&partyID=%s&ticker=%s&field=%s'
FUND = '/api/fund/getFund.csv?etfLof=%s&listStatusCd=%s&secID=%s&ticker=%s&category=%s&operationMode=%s&field=%s'
FUNDNAV = '/api/fund/getFundNav.csv?dataDate=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDDIVM = '/api/fund/getFundDivm.csv?dataDate=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDDIV = '/api/fund/getFundDiv.csv?secID=%s&ticker=%s&adjustedType=%s&beginDate=%s&endDate=%s&field=%s'
FUNDASSETS = '/api/fund/getFundAssets.csv?reportDate=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDHOLDINGS = '/api/fund/getFundHoldings.csv?reportDate=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&secType=%s&field=%s'
FDMTBS = '/api/fundamental/getFdmtBS.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTBSBANK = '/api/fundamental/getFdmtBSBank.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTBSSECU = '/api/fundamental/getFdmtBSSecu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTBSINDU = '/api/fundamental/getFdmtBSIndu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTBSINSU = '/api/fundamental/getFdmtBSInsu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTCF = '/api/fundamental/getFdmtCF.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTCFBANK = '/api/fundamental/getFdmtCFBank.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTCFSECU = '/api/fundamental/getFdmtCFSecu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTCFINDU = '/api/fundamental/getFdmtCFIndu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTCFINSU = '/api/fundamental/getFdmtCFInsu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTIS = '/api/fundamental/getFdmtIS.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTISBANK = '/api/fundamental/getFdmtISBank.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTISSECU = '/api/fundamental/getFdmtISSecu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTISINDU = '/api/fundamental/getFdmtISIndu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTISINSU = '/api/fundamental/getFdmtISInsu.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTEE = '/api/fundamental/getFdmtEe.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
FDMTEF = '/api/fundamental/getFdmtEf.csv?reportType=%s&secID=%s&ticker=%s&beginDate=%s&endDate=%s&forecastType=%s&publishDateBegin=%s&publishDateEnd=%s&field=%s'
TRADECAL = '/api/master/getTradeCal.csv?exchangeCD=%s&beginDate=%s&endDate=%s&field=%s'
INDUSTRY = '/api/master/getIndustry.csv?industryVersion=%s&industryVersionCD=%s&industryLevel=%s&isNew=%s&field=%s'
FSTTOTAL = '/api/equity/getFstTotal.csv?beginDate=%s&endDate=%s&exchangeCD=%s&field=%s'
FSTDETAIL = '/api/equity/getFstDetail.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTBLOCKD = '/api/market/getMktBlockd.csv?secID=%s&ticker=%s&tradeDate=%s&assetClass=%s&beginDate=%s&endDate=%s&field=%s'
HKEQU = '/api/HKequity/getHKEqu.csv?listStatusCD=%s&secID=%s&ticker=%s&field=%s'
HKEQUCA = '/api/HKequity/getHKEquCA.csv?secID=%s&ticker=%s&eventTypeCD=%s&field=%s'
MKTREPOD = '/api/market/getMktRepod.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
MKTBONDD = '/api/market/getMktBondd.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
EQUSHARE = '/api/equity/getEquShare.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&partyID=%s&field=%s'
REPO = '/api/bond/getRepo.csv?secID=%s&ticker=%s&field=%s'
MKTHKEQUD = '/api/market/getMktHKEqud.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
TICKRTSNAPSHOT = '/api/market/getTickRTSnapshot.csv?securityID=%s&field=%s'
TICKRTSNAPSHOTINDEX = '/api/market/getTickRTSnapshotIndex.csv?securityID=%s&field=%s'
FUTURETICKRTSNAPSHOT = '/api/market/getFutureTickRTSnapshot.csv?instrumentID=%s&field=%s'
TICKRTINTRADAY = '/api/market/getTickRTIntraDay.csv?securityID=%s&endTime=%s&startTime=%s&field=%s'
BARRTINTRADAY = '/api/market/getBarRTIntraDay.csv?securityID=%s&endTime=%s&startTime=%s&field=%s'
BARHISTONEDAY = '/api/market/getBarHistOneDay.csv?securityID=%s&date=%s&endTime=%s&startTime=%s&field=%s'
BARHISTDAYRANGE = '/api/market/getBarHistDateRange.csv?securityID=%s&startDate=%s&&endDate=%s&field=%s'
FUTURETICKRTINTRADAY = '/api/market/getFutureTickRTIntraDay.csv?instrumentID=%s&endTime=%s&startTime=%s&field=%s'
FUTUREBARINDAY = '/api/market/getFutureBarHistOneDay.csv?instrumentID=%s&date=%s&field=%s'
FUTUREBARDATERANGE = '/api/market/getFutureBarHistDateRange.csv?instrumentID=%s&startDate=%s&endDate=%s&field=%s'
STOCKFACTORSONEDAY = '/api/market/getStockFactorsOneDay.csv?tradeDate=%s&secID=%s&ticker=%s&field=%s'
STOCKFACTORSDATERANGE = '/api/market/getStockFactorsDateRange.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTFUNDD = '/api/market/getMktFundd.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
MKTFUTMTR = '/api/market/getMktFutMTR.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTFUTMSR = '/api/market/getMktFutMSR.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTFUTMLR = '/api/market/getMktFutMLR.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDETFPRLIST = '/api/fund/getFundETFPRList.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDETFCONS = '/api/fund/getFundETFCons.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
FUNDRATING = '/api/fund/getFundRating.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
CHINAMACRODATA = '/api/macro/getChinaMacroData.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINAMACROINFO = '/api/macro/getChinaMacroInfo.csv?indicID=%s&indicNameAbbr=%s&parentID=%s&field=%s'
GLOBALMACRODATA = '/api/macro/getGlobalMacroData.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GLOBALMACROINFO = '/api/macro/getGlobalMacroInfo.csv?indicID=%s&indicNameAbbr=%s&parentID=%s&field=%s'
INDUSTRIALDATA = '/api/macro/getIndustrialData.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDUSTRIALINFO = '/api/macro/getIndustrialInfo.csv?indicID=%s&indicNameAbbr=%s&parentID=%s&field=%s'
ECOMMERCEDATA = '/api/macro/getEcommerceData.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEINFO = '/api/macro/getEcommerceInfo.csv?indicID=%s&indicNameAbbr=%s&parentID=%s&field=%s'
MKTMFUTD = '/api/market/getMktMFutd.csv?contractMark=%s&contractObject=%s&mainCon=%s&tradeDate=%s&endDate=%s&startDate=%s&field=%s'
CHINADATAGDP = '/api/macro/getChinaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAECI = '/api/macro/getChinaDataECI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAPMI = '/api/macro/getChinaDataPMI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATACCI = '/api/macro/getChinaDataCCI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAECONOMISTSBOOMINDEX = '/api/macro/getChinaDataEconomistsBoomIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAINDUSTRIALBUSINESSCLIMATEINDEX = '/api/macro/getChinaDataIndustrialBusinessClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATACPI = '/api/macro/getChinaDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAPPI = '/api/macro/getChinaDataPPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAINDUSTRY = '/api/macro/getChinaDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATARETAILSALES = '/api/macro/getChinaDataRetailSales.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATARESIDENTINCOMEEXP = '/api/macro/getChinaDataResidentIncomeExp.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAFAI = '/api/macro/getChinaDataFAI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAREALESTATE = '/api/macro/getChinaDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAFOREIGNTRADE = '/api/macro/getChinaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAFDI = '/api/macro/getChinaDataFDI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAMONEYSTATISTICS = '/api/macro/getChinaDataMoneyStatistics.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAALLSYSTEMFINANCING = '/api/macro/getChinaDataAllSystemFinancing.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATALENDINGDEPOSIT = '/api/macro/getChinaDataLendingDeposit.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATACREDITFUNDSTABLE = '/api/macro/getChinaDataCreditFundsTable.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAOPENMARKETOPERATION = '/api/macro/getChinaDataOpenMarketOperation.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAEXCHANGERATE = '/api/macro/getChinaDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAINTERESTRATELENDINGDEPOSIT = '/api/macro/getChinaDataInterestRateLendingDeposit.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAINTERESTRATESHIBOR = '/api/macro/getChinaDataInterestRateSHIBOR.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAINTERESTRATEINTERBANKREPO = '/api/macro/getChinaDataInterestRateInterbankRepo.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAFINANCE = '/api/macro/getChinaDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CHINADATAGOLDCLOSEPRICE = '/api/macro/getChinaDataGoldClosePrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAGDP = '/api/macro/getUSDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAFOREIGNTRADE = '/api/macro/getUSDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAPRICEINDEX = '/api/macro/getUSDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAEMPLOYMENTUNEMPLOYMENT = '/api/macro/getUSDataEmploymentUnemployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAINTERESTRATE = '/api/macro/getUSDataInterestRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAEXCHANGERATE = '/api/macro/getUSDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAMONEYSUPPLY = '/api/macro/getUSDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATACONSUMERCREDIT = '/api/macro/getUSDataConsumerCredit.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATACLIMATEINDEX = '/api/macro/getUSDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATADURABLEGOODS = '/api/macro/getUSDataDurableGoods.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATAREALESTATE = '/api/macro/getUSDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
USDATADOMESTICTRADE = '/api/macro/getUSDataDomesticTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAGDP = '/api/macro/getEUDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAFOREIGNTRADE = '/api/macro/getEUDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAPRICEINDEX = '/api/macro/getEUDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAEMPLOYMENTUNEMPLOYMENT = '/api/macro/getEUDataEmploymentUnemployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAINTERESTRATE = '/api/macro/getEUDataInterestRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAEXCHANGERATE = '/api/macro/getEUDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATABANKING = '/api/macro/getEUDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATACLIMATEINDEX = '/api/macro/getEUDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATAINDUSTRY = '/api/macro/getEUDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
EUDATARETAIL = '/api/macro/getEUDataRetail.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWITZERLANDDATAGDP = '/api/macro/getSwitzerlandDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWITZERLANDDATAPRICEINDEX = '/api/macro/getSwitzerlandDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWITZERLANDDATACLIMATEINDEX = '/api/macro/getSwitzerlandDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWITZERLANDDATAMONEYSUPPLY = '/api/macro/getSwitzerlandDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWEDENDATAGDP = '/api/macro/getSwedenDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWEDENDATAPRICEINDEX = '/api/macro/getSwedenDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SWEDENDATAFOREIGNTRADE = '/api/macro/getSwedenDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAGDP = '/api/macro/getKoreaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAPRICEINDEX = '/api/macro/getKoreaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAEMPLOYMENTUNEMPLOYMENT = '/api/macro/getKoreaDataEmploymentUnemployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAINTERESTRATES = '/api/macro/getKoreaDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAEXCHANGERATE = '/api/macro/getKoreaDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAMONEYSUPPLY = '/api/macro/getKoreaDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATACLIMATEINDEX = '/api/macro/getKoreaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATA_EXTERNALDEBT = '/api/macro/getKoreaData_ExternalDebt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAINDUSTRYANDSERVICE = '/api/macro/getKoreaDataIndustryandService.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
KOREADATAREALESTATE = '/api/macro/getKoreaDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AUSTRALIADATAGDP = '/api/macro/getAustraliaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AUSTRALIADATAFOREIGNTRADE = '/api/macro/getAustraliaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AUSTRALIADATAPRICEINDEX = '/api/macro/getAustraliaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AUSTRALIADATAEMPLOYMENT = '/api/macro/getAustraliaDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AUSTRALIADATACLIMATEINDEX = '/api/macro/getAustraliaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAGDP = '/api/macro/getItalyDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAPAYMENTSBALANCE = '/api/macro/getItalyDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAPRICEINDEX = '/api/macro/getItalyDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAEMPLOYMENT = '/api/macro/getItalyDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAFINANCE = '/api/macro/getItalyDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATACLIMATEINDEX = '/api/macro/getItalyDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ITALYDATAINTERESTRATE = '/api/macro/getItalyDataInterestRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATAGDP = '/api/macro/getSpainDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATAFOREIGNTRADE = '/api/macro/getSpainDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATAPAYMENTSBALANCE = '/api/macro/getSpainDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATABANKING = '/api/macro/getSpainDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATATRANSPORTATION = '/api/macro/getSpainDataTransportation.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATAENERGY = '/api/macro/getSpainDataEnergy.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SPAINDATAFINANCE = '/api/macro/getSpainDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAGDP = '/api/macro/getCanadaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAPAYMENTSBALANCE = '/api/macro/getCanadaDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAFOREIGNTRADE = '/api/macro/getCanadaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAPRICEINDEX = '/api/macro/getCanadaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATABANKING = '/api/macro/getCanadaDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAEMPLOYMENT = '/api/macro/getCanadaDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAMANUFACTURING = '/api/macro/getCanadaDataManufacturing.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATAREALESTATE = '/api/macro/getCanadaDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CANADADATACLIMATEINDEX = '/api/macro/getCanadaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAGDP = '/api/macro/getHKDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAFOREIGNTRADE = '/api/macro/getHKDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAPRICEINDEX = '/api/macro/getHKDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAFINANCE = '/api/macro/getHKDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATABANKING = '/api/macro/getHKDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAINDUSTRY = '/api/macro/getHKDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATACONSUMPTION = '/api/macro/getHKDataConsumption.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATATHROUGHPUT = '/api/macro/getHKDataThroughput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAEMPLOYMENT = '/api/macro/getHKDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAINTERESTRATE = '/api/macro/getHKDataInterestRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAEXCHANGERATE = '/api/macro/getHKDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATAREALESTATE = '/api/macro/getHKDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HKDATATOURISM = '/api/macro/getHKDataTourism.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAGDP = '/api/macro/getIndiaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAPAYMENTSBALANCE = '/api/macro/getIndiaDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAPRICEINDEX = '/api/macro/getIndiaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATATOURISM = '/api/macro/getIndiaDataTourism.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAENERGY = '/api/macro/getIndiaDataEnergy.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATACLIMATEINDEX = '/api/macro/getIndiaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATABANKING = '/api/macro/getIndiaDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAINDUSTRY = '/api/macro/getIndiaDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDIADATAFOREIGNTRADE = '/api/macro/getIndiaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAGDP = '/api/macro/getMalaysiaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAPAYMENTSBALANCE = '/api/macro/getMalaysiaDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAFOREIGNTRADE = '/api/macro/getMalaysiaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAPRICEINDEX = '/api/macro/getMalaysiaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAEMPLOYMENT = '/api/macro/getMalaysiaDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAINDUSTRY = '/api/macro/getMalaysiaDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAFINANCE = '/api/macro/getMalaysiaDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAMONEYSUPPLY = '/api/macro/getMalaysiaDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MALAYSIADATAREALESTATE = '/api/macro/getMalaysiaDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAGDP = '/api/macro/getIndonesiaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAPAYMENTSBALANCE = '/api/macro/getIndonesiaDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAFOREIGNTRADE = '/api/macro/getIndonesiaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAPRICEINDEX = '/api/macro/getIndonesiaDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAINDUSTRY = '/api/macro/getIndonesiaDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATAFINANCE = '/api/macro/getIndonesiaDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATABANKING = '/api/macro/getIndonesiaDataBanking.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATASECURITY = '/api/macro/getIndonesiaDataSecurity.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INDONESIADATATOURISM = '/api/macro/getIndonesiaDataTourism.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAGDP = '/api/macro/getTurkeyDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAPAYMENTSBALANCE = '/api/macro/getTurkeyDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAFOREIGNTRADE = '/api/macro/getTurkeyDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAPRICEINDEX = '/api/macro/getTurkeyDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAEMPLOYMENT = '/api/macro/getTurkeyDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAINDUSTRY = '/api/macro/getTurkeyDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAFINANCE = '/api/macro/getTurkeyDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TURKEYDATAMONEYSUPPLY = '/api/macro/getTurkeyDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAGDP = '/api/macro/getThailandDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAPAYMENTSBALANCE = '/api/macro/getThailandDataPaymentsBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAFOREIGNTRADE = '/api/macro/getThailandDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAPRICEINDEX = '/api/macro/getThailandDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAEMPLOYMENT = '/api/macro/getThailandDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAINDUSTRY = '/api/macro/getThailandDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAFINANCE = '/api/macro/getThailandDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATACLIMATEINDEX = '/api/macro/getThailandDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THAILANDDATAMONEYSUPPLY = '/api/macro/getThailandDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAGDP = '/api/macro/getUKDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAFOREIGNTRADE = '/api/macro/getUKDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATACPI = '/api/macro/getUKDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATARPI = '/api/macro/getUKDataRPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAEMPLOYMENT = '/api/macro/getUKDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAMONEYSUPPLY = '/api/macro/getUKDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATACONSUMERCREDIT = '/api/macro/getUKDataConsumerCredit.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATACLIMATEINDEX = '/api/macro/getUKDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAFINANCE = '/api/macro/getUKDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAINDUSTRIALPI = '/api/macro/getUKDataIndustrialPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAHOUSEPI = '/api/macro/getUKDataHousePI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAINTERESTRATES = '/api/macro/getUKDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UKDATAEXCHANGERATE = '/api/macro/getUKDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAGDP = '/api/macro/getJapanDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAFOREIGNTRADE = '/api/macro/getJapanDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATACPI = '/api/macro/getJapanDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAEMPLOYMENT = '/api/macro/getJapanDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAMONEYSUPPLY = '/api/macro/getJapanDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATACLIMATEINDEX = '/api/macro/getJapanDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAINDUSTRIALPI = '/api/macro/getJapanDataIndustrialPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAHOUSEPI = '/api/macro/getJapanDataHousePI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAINTERESTRATES = '/api/macro/getJapanDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
JAPANDATAEXCHANGERATE = '/api/macro/getJapanDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAGDP = '/api/macro/getGermanyDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAFOREIGNTRADE = '/api/macro/getGermanyDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATACPI = '/api/macro/getGermanyDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAPPI = '/api/macro/getGermanyDataPPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAIMPORTEXPORTPI = '/api/macro/getGermanyDataImportExportPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAEMPLOYMENT = '/api/macro/getGermanyDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAMONEYSUPPLY = '/api/macro/getGermanyDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATACLIMATEINDEX = '/api/macro/getGermanyDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAFINANCE = '/api/macro/getGermanyDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAINDUSTRIALPI = '/api/macro/getGermanyDataIndustrialPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAREALESTATE = '/api/macro/getGermanyDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATADOMESTICTRADE = '/api/macro/getGermanyDataDomesticTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
GERMANYDATAINTERESTRATES = '/api/macro/getGermanyDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAFINANCE = '/api/macro/getFranceDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAGDP = '/api/macro/getFranceDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAFOREIGNTRADE = '/api/macro/getFranceDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATACPI = '/api/macro/getFranceDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAPPI = '/api/macro/getFranceDataPPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAIMPORTPI = '/api/macro/getFranceDataImportPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAEMPLOYMENT = '/api/macro/getFranceDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAMONEYSUPPLY = '/api/macro/getFranceDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATACLIMATEINDEX = '/api/macro/getFranceDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAINDUSTRIALPI = '/api/macro/getFranceDataIndustrialPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATADOMESTICTRADE = '/api/macro/getFranceDataDomesticTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FRANCEDATAINTERESTRATES = '/api/macro/getFranceDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAGDP = '/api/macro/getTaiwanDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAEXTERNALDEBT = '/api/macro/getTaiwanDataExternalDebt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAFOREIGNTRADE = '/api/macro/getTaiwanDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATACPI = '/api/macro/getTaiwanDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAIMPORTEXPORTPI = '/api/macro/getTaiwanDataImportExportPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAEMPLOYMENT = '/api/macro/getTaiwanDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAMONEYSUPPLY = '/api/macro/getTaiwanDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATALENDINGDEPOSIT = '/api/macro/getTaiwanDataLendingDeposit.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATARESERVEFUND = '/api/macro/getTaiwanDataReserveFund.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATACLIMATEINDEX = '/api/macro/getTaiwanDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAFINANCE = '/api/macro/getTaiwanDataFinance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAINDUSTRIALPI = '/api/macro/getTaiwanDataIndustrialPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAREALESTATE = '/api/macro/getTaiwanDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATATOURISM = '/api/macro/getTaiwanDataTourism.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATACROSSSTRAITTRADE = '/api/macro/getTaiwanDataCrossStraitTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATABUSINESSANDECONOMY = '/api/macro/getTaiwanDataBusinessandEconomy.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAINTERESTRATES = '/api/macro/getTaiwanDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TAIWANDATAEXCHANGERATE = '/api/macro/getTaiwanDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAGDP = '/api/macro/getMacaoDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAPRICEINDEX = '/api/macro/getMacaoDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAEMPLOYMENT = '/api/macro/getMacaoDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAMONEYSUPPLY = '/api/macro/getMacaoDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAFOREIGNEXCHANGERESERVES = '/api/macro/getMacaoDataForeignExchangeReserves.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATATOURISM = '/api/macro/getMacaoDataTourism.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAGAMINGINDUSTRY = '/api/macro/getMacaoDataGamingIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAINTERESTRATES = '/api/macro/getMacaoDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MACAODATAEXCHANGERATE = '/api/macro/getMacaoDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATAGDP = '/api/macro/getRussiaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATAFOREIGNTRADE = '/api/macro/getRussiaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATACPI = '/api/macro/getRussiaDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATAMONEYSUPPLY = '/api/macro/getRussiaDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATACLIMATEINDEX = '/api/macro/getRussiaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATAINTERESTRATES = '/api/macro/getRussiaDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
RUSSIADATAEXCHANGERATE = '/api/macro/getRussiaDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAGDP = '/api/macro/getBrazilDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAFOREIGNTRADE = '/api/macro/getBrazilDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAPRICEINDEX = '/api/macro/getBrazilDataPriceIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAEMPLOYMENT = '/api/macro/getBrazilDataEmployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAMONEYSUPPLY = '/api/macro/getBrazilDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATACLIMATEINDEX = '/api/macro/getBrazilDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATARETAILSALE = '/api/macro/getBrazilDataRetailSale.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAINTERESTRATES = '/api/macro/getBrazilDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BRAZILDATAEXCHANGERATE = '/api/macro/getBrazilDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAGDP = '/api/macro/getSouthAfricaDataGDP.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAEMPLOYMENTUNEMPLOYMENT = '/api/macro/getSouthAfricaDataEmploymentUnemployment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAFOREIGNTRADE = '/api/macro/getSouthAfricaDataForeignTrade.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATACPI = '/api/macro/getSouthAfricaDataCPI.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAMONEYSUPPLY = '/api/macro/getSouthAfricaDataMoneySupply.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATACLIMATEINDEX = '/api/macro/getSouthAfricaDataClimateIndex.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAINDUSTRY = '/api/macro/getSouthAfricaDataIndustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAREALESTATE = '/api/macro/getSouthAfricaDataRealEstate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATARETAILSALES = '/api/macro/getSouthAfricaDataRetailSales.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAINTERESTRATES = '/api/macro/getSouthAfricaDataInterestRates.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SOUTHAFRICADATAEXCHANGERATE = '/api/macro/getSouthAfricaDataExchangeRate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AGRICDATAPRICE = '/api/macro/getAgricDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AGRICDATAOUTPV = '/api/macro/getAgricDataOutpV.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AGRICDATAWASDE = '/api/macro/getAgricDataWASDE.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
AGRICDATAIMPTEXPT = '/api/macro/getAgricDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FOODBVGDATAPRICE = '/api/macro/getFoodBvgDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FOODBVGDATASALESOUTPUT = '/api/macro/getFoodBvgDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FOODBVGDATAIMPTEXPT = '/api/macro/getFoodBvgDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
COMMTRADEDATATRSCG = '/api/macro/getCommTradeDataTRSCG.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
COMMTRADEDATASALES50LARGEEN = '/api/macro/getCommTradeDataSales50LargeEn.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
COMMTRADEDATAINDEXKEYCIRCEN = '/api/macro/getCommTradeDataIndexKeyCircEn.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CATERTOURDATATRSCG = '/api/macro/getCaterTourDataTRSCG.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CATERTOURDATAHOTELSOPER = '/api/macro/getCaterTourDataHotelsOper.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CATERTOURDATANEWHOTEL = '/api/macro/getCaterTourDataNewHotel.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CATERTOURDATAINBOUNDTOUR = '/api/macro/getCaterTourDataInboundTour.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BIOMEDICINEDATASALESOUTPUT = '/api/macro/getBioMedicineDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BIOMEDICINEDATAIMPTEXPT = '/api/macro/getBioMedicineDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
PETROCHEMDATAPRICE = '/api/macro/getPetrochemDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
PETROCHEMDATASALESOUTPUT = '/api/macro/getPetrochemDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
PETROCHEMDATAIMPTEXPT = '/api/macro/getPetrochemDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CLOTHTEXDATAPRICE = '/api/macro/getClothTexDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CLOTHTEXDATASALESOUTPUT = '/api/macro/getClothTexDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CLOTHTEXDATACOTTONWASDE = '/api/macro/getClothTexDataCottonWASDE.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
CLOTHTEXDATAIMPTEXPT = '/api/macro/getClothTexDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
LIGHTMANUFDATAPRICE = '/api/macro/getLightManufDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
LIGHTMANUFDATASALESOUTPUT = '/api/macro/getLightManufDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
LIGHTMANUFDATAIMPTEXPT = '/api/macro/getLightManufDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MININGDATAPRICE = '/api/macro/getMiningDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MININGDATAOUTPSALESTRANSP = '/api/macro/getMiningDataOutpSalesTransp.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MININGDATAIMPTEXPT = '/api/macro/getMiningDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FERMETALDATAPRICE = '/api/macro/getFerMetalDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FERMETALDATASALESOUTPUT = '/api/macro/getFerMetalDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
FERMETALDATAIMPTEXPT = '/api/macro/getFerMetalDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
NONFERMETALDATAPRICE = '/api/macro/getNonferMetalDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
NONFERMETALDATASALESOUTPUT = '/api/macro/getNonferMetalDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
NONFERMETALDATAIMPTEXPT = '/api/macro/getNonferMetalDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
DELIVERYEQDATAPRICE = '/api/macro/getDeliveryEqDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
DELIVERYEQDATASALESOUTPUT = '/api/macro/getDeliveryEqDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
DELIVERYEQDATAIMPTEXPT = '/api/macro/getDeliveryEqDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TRAFFICTRANSDATARAILWAY = '/api/macro/getTrafficTransDataRailway.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TRAFFICTRANSDATAROAD = '/api/macro/getTrafficTransDataRoad.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TRAFFICTRANSDATAWATERWAY = '/api/macro/getTrafficTransDataWaterway.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
TRAFFICTRANSDATAAIR = '/api/macro/getTrafficTransDataAir.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UTILINDUSTRYDATAPOWER = '/api/macro/getUtilIndustryDataPower.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UTILINDUSTRYDATAWATER = '/api/macro/getUtilIndustryDataWater.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UTILINDUSTRYDATAGAS = '/api/macro/getUtilIndustryDataGas.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
UTILINDUSTRYDATAENVIRPROT = '/api/macro/getUtilIndustryDataEnvirProt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ELECCOMPDATAPRICE = '/api/macro/getElecCompDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ELECCOMPDATASALESOUTPUT = '/api/macro/getElecCompDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ELECCOMPDATAIMPTEXPT = '/api/macro/getElecCompDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOEQPTDATAPRICE = '/api/macro/getInfoEqptDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOEQPTDATASALESOUTPUT = '/api/macro/getInfoEqptDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOEQPTDATAIMPTEXPT = '/api/macro/getInfoEqptDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HOUSEHOLDAPLSDATASALESOUTPUT = '/api/macro/getHouseholdAplsDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
HOUSEHOLDAPLSDATAIMPTEXPT = '/api/macro/getHouseholdAplsDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOSERVDATASOFTWARE = '/api/macro/getInfoServDataSoftware.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOSERVDATACOMM = '/api/macro/getInfoServDataComm.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INFOSERVDATAINTERNET = '/api/macro/getInfoServDataInternet.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
REALESTDATAPRICE = '/api/macro/getRealEstDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
REALESTDATAINVESTDVPT = '/api/macro/getRealEstDataInvestDvpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
REALESTDATALAND = '/api/macro/getRealEstDataLand.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
REALESTDATASALES = '/api/macro/getRealEstDataSales.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BLDGMATERDATAPRICE = '/api/macro/getBldgMaterDataPrice.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BLDGMATERDATASALESOUTPUT = '/api/macro/getBldgMaterDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MCHNREQPTDATASALESOUTPUT = '/api/macro/getMchnrEqptDataSalesOutput.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
MCHNREQPTDATAIMPTEXPT = '/api/macro/getMchnrEqptDataImptExpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BANKDATAASSETSLIABILITIES = '/api/macro/getBankDataAssetsLiabilities.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
BANKDATANONPERFORMINGLOANS = '/api/macro/getBankDataNonPerformingLoans.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
SECURITIESDATAOPERINDIC = '/api/macro/getSecuritiesDataOperIndic.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INSDATAPREMPRYINSURANCE = '/api/macro/getInsDataPremPryInsurance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INSDATACLAIMPAYMENT = '/api/macro/getInsDataClaimPayment.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INSDATAFUNDBALANCE = '/api/macro/getInsDataFundBalance.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
INSDATAASSETS = '/api/macro/getInsDataAssets.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAYILI = '/api/macro/getEcommerceDataYili.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAGUANGMING = '/api/macro/getEcommerceDataGuangming.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACHENGDELOLO = '/api/macro/getEcommerceDataChengDeLolo.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAQIAQIA = '/api/macro/getEcommerceDataQiaqia.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAVVGROUP = '/api/macro/getEcommerceDataVVGroup.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJINFENGWINE = '/api/macro/getEcommerceDataJinfengWine.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAGUYUELONGSHAN = '/api/macro/getEcommerceDataGuyueLongshan.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASHANXIFENJIU = '/api/macro/getEcommerceDataShanxiFenjiu.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAZHANGYUA = '/api/macro/getEcommerceDataZhangyuA.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMOGAO = '/api/macro/getEcommerceDataMogao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAKEMENNOODLEMFG = '/api/macro/getEcommerceDataKemenNoodleMFG.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJINZIHAM = '/api/macro/getEcommerceDataJinziHam.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALOTUS = '/api/macro/getEcommerceDataLotus.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABEIYINMATE = '/api/macro/getEcommerceDataBeiyinMate.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAQINGDAOHAIER = '/api/macro/getEcommerceDataQingdaoHaier.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATATCLGROUP = '/api/macro/getEcommerceDataTCLGroup.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMIDEAGROUP = '/api/macro/getEcommerceDataMideaGroup.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAWHIRLPOOL = '/api/macro/getEcommerceDataWhirlpool.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJOYOUNG = '/api/macro/getEcommerceDataJoyoung.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAVATTI = '/api/macro/getEcommerceDataVatti.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASUPOR = '/api/macro/getEcommerceDataSupor.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAKONKA = '/api/macro/getEcommerceDataKonka.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACHANGHONG = '/api/macro/getEcommerceDataChanghong.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALITTLESWAN = '/api/macro/getEcommerceDataLittleSwan.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMEILING = '/api/macro/getEcommerceDataMeiling.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAZTE = '/api/macro/getEcommerceDataZTE.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATADATANGTELECOM = '/api/macro/getEcommerceDataDatangTelecom.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABIRD = '/api/macro/getEcommerceDataBird.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATADAHUATECHNOLOGY = '/api/macro/getEcommerceDataDahuaTechnology.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATATSINGHUATONGFANG = '/api/macro/getEcommerceDataTsinghuaTongfang.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHEDY = '/api/macro/getEcommerceDataHedy.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHADAY = '/api/macro/getEcommerceDataHaday.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAYANJINGBEER = '/api/macro/getEcommerceDataYanjingBeer.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMAIQUER = '/api/macro/getEcommerceDataMaiquer.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACITICGUOANWINE = '/api/macro/getEcommerceDataCiticGuoanWine.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAQINGQINGBARLEYWINE = '/api/macro/getEcommerceDataQingqingBarleyWine.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHAOXIANGNI = '/api/macro/getEcommerceDataHaoxiangni.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAFULINGZHACAI = '/api/macro/getEcommerceDataFulingZhacai.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHUANGSHANGHUANG = '/api/macro/getEcommerceDataHuangshanghuang.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHAINANYEDAO = '/api/macro/getEcommerceDataHainanYedao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASHUANGTAFOOD = '/api/macro/getEcommerceDataShuangtaFood.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJIUGUILIQUOR = '/api/macro/getEcommerceDataJiuguiLiquor.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABLACKSESAME = '/api/macro/getEcommerceDataBlackSesame.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAKINGSLUCK = '/api/macro/getEcommerceDataKingsLuck.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALAOBAIGANLIQUOR = '/api/macro/getEcommerceDataLaobaiganLiquor.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASHUANGHUIDVPT = '/api/macro/getEcommerceDataShuanghuiDvpt.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
OPT = '/api/options/getOpt.csv?contractStatus=%s&optID=%s&secID=%s&ticker=%s&varSecID=%s&varticker=%s&field=%s'
MKTOPTD = '/api/market/getMktOptd.csv?optID=%s&secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
SOCIALDATAXQ = '/api/subject/getSocialDataXQ.csv?beginDate=%s&endDate=%s&ticker=%s&field=%s'
SOCIALDATAXQBYTICKER = '/api/subject/getSocialDataXQByTicker.csv?ticker=%s&field=%s'
SOCIALDATAXQBYDATE = '/api/subject/getSocialDataXQByDate.csv?statisticsDate=%s&field=%s'
OPTVAR = '/api/options/getOptVar.csv?exchangeCD=%s&secID=%s&ticker=%s&contractType=%s&exerType=%s&field=%s'
NEWSINFO = '/api/subject/getNewsInfo.csv?newsID=%s&field=%s'
NEWSINFOBYTIME = '/api/subject/getNewsInfoByTime.csv?newsPublishDate=%s&beginTime=%s&endTime=%s&field=%s'
NEWSCONTENT = '/api/subject/getNewsContent.csv?newsID=%s&field=%s'
NEWSCONTENTBYTIME = '/api/subject/getNewsContentByTime.csv?newsPublishDate=%s&beginTime=%s&endTime=%s&field=%s'
COMPANYBYNEWS = '/api/subject/getCompanyByNews.csv?newsID=%s&field=%s'
NEWSBYCOMPANY = '/api/subject/getNewsByCompany.csv?partyID=%s&beginDate=%s&endDate=%s&field=%s'
TICKERSBYNEWS = '/api/subject/getTickersByNews.csv?newsID=%s&field=%s'
NEWSBYTICKERS = '/api/subject/getNewsByTickers.csv?secID=%s&secShortName=%s&ticker=%s&beginDate=%s&endDate=%s&exchangeCD=%s&field=%s'
MKTEQUDADJ = '/api/market/getMktEqudAdj.csv?secID=%s&ticker=%s&tradeDate=%s&beginDate=%s&endDate=%s&field=%s'
MKTADJF = '/api/market/getMktAdjf.csv?secID=%s&ticker=%s&field=%s'
OPTIONTICKRTSNAPSHOT = '/api/market/getOptionTickRTSnapshot.csv?optionId=%s&field=%s'
FUTUREBARRTINTRADAY = '/api/market/getFutureBarRTIntraDay.csv?instrumentID=%s&endTime=%s&startTime=%s&field=%s'
MKTFUTDVOL = '/api/market/getMktFutdVol.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
THEMESCONTENT = '/api/subject/getThemesContent.csv?isMain=%s&themeID=%s&themeName=%s&themeSource=%s&field=%s'
TICKERSBYTHEMES = '/api/subject/getTickersByThemes.csv?themeID=%s&themeName=%s&beginDate=%s&endDate=%s&isNew=%s&field=%s'
THEMESTICKERSINSERT = '/api/subject/getThemesTickersInsert.csv?themeID=%s&themeName=%s&beginDate=%s&endDate=%s&field=%s'
THEMESTICKERSDELETE = '/api/subject/getThemesTickersDelete.csv?themeID=%s&themeName=%s&beginDate=%s&endDate=%s&field=%s'
THEMESBYTICKERS = '/api/subject/getThemesByTickers.csv?secID=%s&secShortName=%s&ticker=%s&beginDate=%s&endDate=%s&exchangeCD=%s&field=%s'
THEMESPERIOD = '/api/subject/getThemesPeriod.csv?isLatest=%s&themeID=%s&themeName=%s&field=%s'
ACTIVETHEMES = '/api/subject/getActiveThemes.csv?date=%s&field=%s'
THEMESSIMILARITY = '/api/subject/getThemesSimilarity.csv?themeID=%s&themeName=%s&field=%s'
THEMESHEAT = '/api/subject/getThemesHeat.csv?themeID=%s&themeName=%s&beginDate=%s&endDate=%s&field=%s'
SECTORTHEMESBYTICKERS = '/api/subject/getSectorThemesByTickers.csv?secID=%s&secShortName=%s&ticker=%s&beginDate=%s&endDate=%s&exchangeCD=%s&field=%s'
WEBTHEMESBYTICKERS = '/api/subject/getWebThemesByTickers.csv?secID=%s&secShortName=%s&ticker=%s&beginDate=%s&endDate=%s&exchangeCD=%s&field=%s'
MKTEQUDLATELY = '/api/market/getMktEqudLately.csv?field=%s'
FDMTISLATELY = '/api/fundamental/getFdmtISLately.csv?field=%s'
NEWSHEATINDEX = '/api/subject/getNewsHeatIndex.csv?beginDate=%s&endDate=%s&exchangeCD=%s&secID=%s&secShortName=%s&ticker=%s&field=%s'
NEWSSENTIMENTINDEX = '/api/subject/getNewsSentimentIndex.csv?beginDate=%s&endDate=%s&exchangeCD=%s&secID=%s&secShortName=%s&ticker=%s&field=%s'
SECTYPEREL = '/api/master/getSecTypeRel.csv?secID=%s&ticker=%s&typeID=%s&field=%s'
REPORTBYTICKER = '/api/subject/getReportByTicker.csv?ticker=%s&beginDate=%s&endDate=%s&field=%s'
REPORTBYCATEGORY = '/api/subject/getReportByCategory.csv?beginDate=%s&Category=%s&endDate=%s&field=%s'
REPORTCONTENT = '/api/subject/getReportContent.csv?ticker=%s&beginDate=%s&endDate=%s&field=%s'
MKTLIMIT = '/api/market/getMktLimit.csv?secID=%s&ticker=%s&tradeDate=%s&field=%s'
ECOMMERCEDATAWULIANGYE = '/api/macro/getEcommerceDataWuliangye.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAGREE = '/api/macro/getEcommerceDataGree.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHISENSEELECTRIC = '/api/macro/getEcommerceDataHisenseElectric.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHISENSE = '/api/macro/getEcommerceDataHisense.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJIAJIAFOOD = '/api/macro/getEcommerceDataJiajiaFood.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAROBAM = '/api/macro/getEcommerceDataRobam.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAASD = '/api/macro/getEcommerceDataASD.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMACRO = '/api/macro/getEcommerceDataMacro.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAELECPRO = '/api/macro/getEcommerceDataElecpro.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASANGLEJIN = '/api/macro/getEcommerceDataSanglejin.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHOMA = '/api/macro/getEcommerceDataHoma.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALONGDAMEAT = '/api/macro/getEcommerceDataLongdaMeat.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABYHEALTH = '/api/macro/getEcommerceDataByHealth.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHAIXIN = '/api/macro/getEcommerceDataHaixin.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAVANWARD = '/api/macro/getEcommerceDataVanward.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMEIDA = '/api/macro/getEcommerceDataMeida.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHENGSHUNVINEGARINDUSTRY = '/api/macro/getEcommerceDataHengshunVinegarindustry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASHUIJINGFANG = '/api/macro/getEcommerceDataShuijingfang.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACHUNLAN = '/api/macro/getEcommerceDataChunlan.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAYILITE = '/api/macro/getEcommerceDataYilite.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHUANGSHI = '/api/macro/getEcommerceDataHuangshi.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAYANGHE = '/api/macro/getEcommerceDataYanghe.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASANYUAN = '/api/macro/getEcommerceDataSanyuan.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATATUOPAISHEDE = '/api/macro/getEcommerceDataTuopaiShede.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAKUAIJISHAN = '/api/macro/getEcommerceDataKuaijishan.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATATONGHUA = '/api/macro/getEcommerceDataTonghua.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAKWEICHOWMOUTAIGROUP = '/api/macro/getEcommerceDataKweichowMoutaiGroup.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATATSINGTAO = '/api/macro/getEcommerceDataTsingTao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ACTIVETHEMESINSERT = '/api/subject/getActiveThemesInsert.csv?beginDate=%s&endDate=%s&isLatest=%s&themeSource=%s&field=%s'
ACTIVETHEMESDELETE = '/api/subject/getActiveThemesDelete.csv?beginDate=%s&endDate=%s&isLatest=%s&themeSource=%s&field=%s'
EQUINFO = '/api/master/getEquInfo.csv?ticker=%s&field=%s'
SECTIPS = '/api/market/getSecTips.csv?tipsTypeCD=%s&field=%s'
THEMESCLUSTER = '/api/subject/getThemesCluster.csv?isMain=%s&themeID=%s&themeName=%s&field=%s'
THEMESBYNEWS = '/api/subject/getThemesByNews.csv?insertDate=%s&insertDate=%s&newsID=%s&beginTime=%s&endTime=%s&field=%s'
BARRTINTRADAYONEMINUTE = '/api/market/getBarRTIntraDayOneMinute.csv?time=%s&field=%s'
EQURTRANK = '/api/market/getEquRTRank.csv?desc=%s&exchangeCD=%s&field=%s'
ECOMMERCEDATAGUJING = '/api/macro/getEcommerceDataGujing.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALUZHOULAOJIAO = '/api/macro/getEcommerceDataLuzhouLaojiao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASHANGHAIMALING = '/api/macro/getEcommerceDataShanghaiMaling.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABLACKCATTLEFOOD = '/api/macro/getEcommerceDataBlackCattleFood.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATADELISI = '/api/macro/getEcommerceDataDelisi.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASTARLAKEBIOSCIENCE = '/api/macro/getEcommerceDataStarLakeBioscience.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJONJEEHITECH = '/api/macro/getEcommerceDataJonjeeHiTech.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACRSANJIU = '/api/macro/getEcommerceDataCRSanjiu.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAJIUZHITANG = '/api/macro/getEcommerceDataJiuzhitang.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAFUANNA = '/api/macro/getEcommerceDataFuanna.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALUOLAI = '/api/macro/getEcommerceDataLuolai.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAGUIRENNIAO = '/api/macro/getEcommerceDataGuirenniao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATABAOXINIAO = '/api/macro/getEcommerceDataBaoxiniao.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATALAOFENGXIANG = '/api/macro/getEcommerceDataLaofengxiang.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAFIYTAA = '/api/macro/getEcommerceDataFiytaA.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAGOLDLEAFJEWELRY = '/api/macro/getEcommerceDataGoldleafJewelry.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACOMIXGROUP = '/api/macro/getEcommerceDataComixGroup.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAYAOJIPLAYINGCARD = '/api/macro/getEcommerceDataYaojiPlayingCard.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAMGSTATIONERY = '/api/macro/getEcommerceDataMGStationery.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACS = '/api/macro/getEcommerceDataCS.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAEDIFIER = '/api/macro/getEcommerceDataEdifier.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAHIKVISION = '/api/macro/getEcommerceDataHikVision.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATASOLAREAST = '/api/macro/getEcommerceDataSolareast.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATACHIGO = '/api/macro/getEcommerceDataChigo.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
ECOMMERCEDATAAUCMA = '/api/macro/getEcommerceDataAucma.csv?indicID=%s&indicName=%s&beginDate=%s&endDate=%s&field=%s'
THEMESBYNEWSCOMPANYREL = '/api/subject/getThemesByNewsCompanyRel.csv?insertDate=%s&insertDate=%s&newsID=%s&beginTime=%s&endTime=%s&field=%s'
THEMESINSERTDB = '/api/subject/getThemesInsertDB.csv?beginDate=%s&endDate=%s&themeSource=%s&field=%s'
THEMESBYNEWSLF = '/api/subject/getThemesByNewsLF.csv?insertDate=%s&insertDate=%s&newsID=%s&beginTime=%s&endTime=%s&field=%s'
THEMESBYNEWSMF = '/api/subject/getThemesByNewsMF.csv?insertDate=%s&insertDate=%s&newsID=%s&beginTime=%s&endTime=%s&field=%s'
INDUSTRYTICKRTSNAPSHOT = '/api/market/getIndustryTickRTSnapshot.csv?securityID=%s&field=%s'
NEWSINFOBYINSERTTIME = '/api/subject/getNewsInfoByInsertTime.csv?newsInsertDate=%s&beginTime=%s&endTime=%s&field=%s'
NEWSCONTENTBYINSERTTIME = '/api/subject/getNewsContentByInsertTime.csv?newsInsertDate=%s&beginTime=%s&endTime=%s&field=%s'
SECTYPEREGIONREL = '/api/master/getSecTypeRegionRel.csv?secID=%s&ticker=%s&typeID=%s&field=%s'
SECTYPE = '/api/master/getSecType.csv?field=%s'
SECTYPEREGION = '/api/master/getSecTypeRegion.csv?field=%s'
SOCIALDATAGUBA = '/api/subject/getSocialDataGuba.csv?beginDate=%s&endDate=%s&ticker=%s&field=%s'
SOCIALTHEMEDATAGUBA = '/api/subject/getSocialThemeDataGuba.csv?beginDate=%s&endDate=%s&themeID=%s&field=%s'
FUNDSHARESCHG = '/api/fund/getFundSharesChg.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
THEMESBYNEWSTIME = '/api/subject/getThemesByNewsTime.csv?publishBeginTime=%s&publishEndTime=%s&field=%s'
THEMESBYNEWSTIMECOMPANYREL = '/api/subject/getThemesByNewsTimeCompanyRel.csv?publishBeginTime=%s&publishEndTime=%s&field=%s'
THEMESBYNEWSTIMELF = '/api/subject/getThemesByNewsTimeLF.csv?publishBeginTime=%s&publishEndTime=%s&field=%s'
THEMESBYNEWSTIMEMF = '/api/subject/getThemesByNewsTimeMF.csv?publishBeginTime=%s&publishEndTime=%s&field=%s'
MKTFUNDDADJAF = '/api/market/getMktFunddAdjAf.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
REPORTCONTENTBYID = '/api/subject/getReportContentByID.csv?reportID=%s&field=%s'
THEMESBYNEWS2 = '/api/subject/getThemesByNews2.csv?insertBeginTime=%s&insertEndTime=%s&newsID=%s&field=%s'
THEMESBYNEWSTIME2 = '/api/subject/getThemesByNewsTime2.csv?publishBeginTime=%s&publishEndTime=%s&field=%s'
SYSCODE = '/api/master/getSysCode.csv?codeTypeID=%s&valueCD=%s&field=%s'
FUNDLEVERAGEINFO = '/api/fund/getFundLeverageInfo.csv?exchangeCDLeverage=%s&secID=%s&ticker=%s&field=%s'
SECST = '/api/equity/getSecST.csv?secID=%s&ticker=%s&beginDate=%s&endDate=%s&field=%s'
DERIV = '/api/IV/getDerIv.csv?beginDate=%s&endDate=%s&optID=%s&SecID=%s&field=%s'
DERIVHV = '/api/IV/getDerIvHv.csv?beginDate=%s&endDate=%s&SecID=%s&period=%s&field=%s'
DERIVINDEX = '/api/IV/getDerIvIndex.csv?beginDate=%s&endDate=%s&SecID=%s&period=%s&field=%s'
DERIVIVPDELTA = '/api/IV/getDerIvIvpDelta.csv?beginDate=%s&endDate=%s&SecID=%s&delta=%s&period=%s&field=%s'
DERIVPARAM = '/api/IV/getDerIvParam.csv?beginDate=%s&endDate=%s&SecID=%s&expDate=%s&field=%s'
DERIVRAWDELTA = '/api/IV/getDerIvRawDelta.csv?beginDate=%s&endDate=%s&SecID=%s&delta=%s&period=%s&field=%s'
DERIVSURFACE = '/api/IV/getDerIvSurface.csv?beginDate=%s&endDate=%s&SecID=%s&contractType=%s&field=%s'
