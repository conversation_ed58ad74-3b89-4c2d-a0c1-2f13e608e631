../../../bin/__pycache__/bt-run.cpython-312.pyc,,
../../../bin/bt-run.py,sha256=chrIeW5Er8x2JixmW_nmwra_4xu2Z7ovEUce4mDuRbw,1176
../../../bin/btrun,sha256=ShejpU6OMQDbb-j36cOgvy8kTvGaKLgtmSYsDyV_zhM,270
backtrader-1.9.78.123.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
backtrader-1.9.78.123.dist-info/LICENSE,sha256=4cCtcomD2KVzNeUs8QZPGv_R1FQXPYzr0-2LSnK0hwQ,35121
backtrader-1.9.78.123.dist-info/METADATA,sha256=I3yEDqmo3Itd3FlSd8JXqlyYyl79nRUNEmyR0VoVRcs,6755
backtrader-1.9.78.123.dist-info/RECORD,,
backtrader-1.9.78.123.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
backtrader-1.9.78.123.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
backtrader-1.9.78.123.dist-info/entry_points.txt,sha256=GaVeeLLzR-C5Awt7C9rVn75IMEtBZ7Zk5PMCwt76Rvs,49
backtrader-1.9.78.123.dist-info/top_level.txt,sha256=VpSC5MJzXVKdKPBeBCoM1rvYa-sYFwvIRP9zmD-dGfU,11
backtrader/__init__.py,sha256=dj9GPMuPDWgUXBnrt5DxuTQlQsUHwCtJF3YzVKgNlDE,2578
backtrader/__pycache__/__init__.cpython-312.pyc,,
backtrader/__pycache__/analyzer.cpython-312.pyc,,
backtrader/__pycache__/broker.cpython-312.pyc,,
backtrader/__pycache__/cerebro.cpython-312.pyc,,
backtrader/__pycache__/comminfo.cpython-312.pyc,,
backtrader/__pycache__/dataseries.cpython-312.pyc,,
backtrader/__pycache__/errors.cpython-312.pyc,,
backtrader/__pycache__/feed.cpython-312.pyc,,
backtrader/__pycache__/fillers.cpython-312.pyc,,
backtrader/__pycache__/flt.cpython-312.pyc,,
backtrader/__pycache__/functions.cpython-312.pyc,,
backtrader/__pycache__/indicator.cpython-312.pyc,,
backtrader/__pycache__/linebuffer.cpython-312.pyc,,
backtrader/__pycache__/lineiterator.cpython-312.pyc,,
backtrader/__pycache__/lineroot.cpython-312.pyc,,
backtrader/__pycache__/lineseries.cpython-312.pyc,,
backtrader/__pycache__/mathsupport.cpython-312.pyc,,
backtrader/__pycache__/metabase.cpython-312.pyc,,
backtrader/__pycache__/observer.cpython-312.pyc,,
backtrader/__pycache__/order.cpython-312.pyc,,
backtrader/__pycache__/position.cpython-312.pyc,,
backtrader/__pycache__/resamplerfilter.cpython-312.pyc,,
backtrader/__pycache__/signal.cpython-312.pyc,,
backtrader/__pycache__/sizer.cpython-312.pyc,,
backtrader/__pycache__/store.cpython-312.pyc,,
backtrader/__pycache__/strategy.cpython-312.pyc,,
backtrader/__pycache__/talib.cpython-312.pyc,,
backtrader/__pycache__/timer.cpython-312.pyc,,
backtrader/__pycache__/trade.cpython-312.pyc,,
backtrader/__pycache__/tradingcal.cpython-312.pyc,,
backtrader/__pycache__/version.cpython-312.pyc,,
backtrader/__pycache__/writer.cpython-312.pyc,,
backtrader/analyzer.py,sha256=iyFJ1SOWUiqwRyVpqdmJM5crVlAuwgTxegjk-TL6CiQ,14440
backtrader/analyzers/__init__.py,sha256=WhP7CQ5-syHOjOEFalskUxhyMKtFVTyuE77s-WqdubU,1527
backtrader/analyzers/__pycache__/__init__.cpython-312.pyc,,
backtrader/analyzers/__pycache__/annualreturn.cpython-312.pyc,,
backtrader/analyzers/__pycache__/calmar.cpython-312.pyc,,
backtrader/analyzers/__pycache__/drawdown.cpython-312.pyc,,
backtrader/analyzers/__pycache__/leverage.cpython-312.pyc,,
backtrader/analyzers/__pycache__/logreturnsrolling.cpython-312.pyc,,
backtrader/analyzers/__pycache__/periodstats.cpython-312.pyc,,
backtrader/analyzers/__pycache__/positions.cpython-312.pyc,,
backtrader/analyzers/__pycache__/pyfolio.cpython-312.pyc,,
backtrader/analyzers/__pycache__/returns.cpython-312.pyc,,
backtrader/analyzers/__pycache__/sharpe.cpython-312.pyc,,
backtrader/analyzers/__pycache__/sqn.cpython-312.pyc,,
backtrader/analyzers/__pycache__/timereturn.cpython-312.pyc,,
backtrader/analyzers/__pycache__/tradeanalyzer.cpython-312.pyc,,
backtrader/analyzers/__pycache__/transactions.cpython-312.pyc,,
backtrader/analyzers/__pycache__/vwr.cpython-312.pyc,,
backtrader/analyzers/annualreturn.py,sha256=poHmH1j_lTyLXmV3wioemtNtaVREALf9ESKwTitc_Y8,2818
backtrader/analyzers/calmar.py,sha256=kuNTIGqR0pMy03bwE9F8mrM1TMrExEX5uqVNw9xGY6c,3821
backtrader/analyzers/drawdown.py,sha256=QVqABTCgSOU15Us2OZnovionqH5L3ljDuE43ZYwjeRc,6513
backtrader/analyzers/leverage.py,sha256=Lf74_Dwoy1DPLIXPu3caNdRsaITz0SqfZe79e8ErB2U,2397
backtrader/analyzers/logreturnsrolling.py,sha256=jSJ9VznacZljOyb4t4UW4ab0LABM_-Wa3QKWyx6GT7w,5020
backtrader/analyzers/periodstats.py,sha256=PKczntu4YRg3rOX-FoWkgHS5V0MKez37HAXl_vvNrnc,3555
backtrader/analyzers/positions.py,sha256=LV5exJF_T5NxDiHvQMPZ2xLsKf5G0g9I8nhlSHa1h30,2790
backtrader/analyzers/pyfolio.py,sha256=Jv9QGILTt7DIHMXH_IvSDdcEWC2Wv_tcIrQ4OJ-Egqc,5933
backtrader/analyzers/returns.py,sha256=dppUoUYhZ5rHhzdYC0ExBpJLWcQUhocOS2Q_V4upMfs,4698
backtrader/analyzers/sharpe.py,sha256=XjkMmaZnJshzv0yFawrr1d-4mt7BohJo5rB7OWUXODU,7383
backtrader/analyzers/sqn.py,sha256=6WFCIgMSIEyxQjD0W6H2fjXjkA93bnOKwkyBoJuRXR4,2623
backtrader/analyzers/timereturn.py,sha256=CpG1DjHM1cgjR88xZw3TqKTeOsjIhhsBQfiHZ-f-EA0,5210
backtrader/analyzers/tradeanalyzer.py,sha256=idWASsfdLtTqJWkLIVShiHRCCzcYRA9F8RKeV82cqTg,7116
backtrader/analyzers/transactions.py,sha256=VFyOFV9WVRmyfto3_FmHkHCjdUAASz54d5CDKKeGfNk,3562
backtrader/analyzers/vwr.py,sha256=cGlpSdn4uyQD2zABZUSZeGW2r9ogmuKFXcNAXX08LVQ,5460
backtrader/broker.py,sha256=zwKgZRimv0K2bIp118lXsJ9aFu_SfcV2T3igDpsDTrM,5504
backtrader/brokers/__init__.py,sha256=vr40AQdV8VXX0zTZbD4BPxXlt7dEsWsYj2mYP98Kidw,1548
backtrader/brokers/__pycache__/__init__.cpython-312.pyc,,
backtrader/brokers/__pycache__/bbroker.cpython-312.pyc,,
backtrader/brokers/__pycache__/ibbroker.cpython-312.pyc,,
backtrader/brokers/__pycache__/oandabroker.cpython-312.pyc,,
backtrader/brokers/__pycache__/vcbroker.cpython-312.pyc,,
backtrader/brokers/bbroker.py,sha256=D9OXpygU8N4x_NMYtKHaIDBBKum-pCbA1dzdABpSll4,46016
backtrader/brokers/ibbroker.py,sha256=pi6hU5rKedQkVq2s2hpuO2Pj79qgADHtUCCg7GXHgQw,21489
backtrader/brokers/oandabroker.py,sha256=gk_H9J7S-HNuIOr02RSezaxAj1sCt6MhfHRFW6W5COE,12811
backtrader/brokers/vcbroker.py,sha256=B3n_rMWTd3FiMFaK_enAXs51dfF07SfbAAggJvbbJF0,15907
backtrader/btrun/__init__.py,sha256=wMxf2H19mb-iEmRBhcXQFVK6652EbCvpW2d5b4Tbios,1044
backtrader/btrun/__pycache__/__init__.cpython-312.pyc,,
backtrader/btrun/__pycache__/btrun.cpython-312.pyc,,
backtrader/btrun/btrun.py,sha256=81RJP5ySmY5sB4AJMDLW4j3Le0xhtVmHLhj5c8X-cDw,25093
backtrader/cerebro.py,sha256=JG9OLYAfPL-7slJuF_XkST_Znn_FHq_MDXc6CUKD5pY,63600
backtrader/comminfo.py,sha256=SLvjMpK3VIgP68rVym-F4aRoHipPbMYhr0dUQVSBLcQ,11691
backtrader/commissions/__init__.py,sha256=zRfwr_YHF8i07Y60BPfgurl4SaUSppWjfczUOM1NAqE,1796
backtrader/commissions/__pycache__/__init__.cpython-312.pyc,,
backtrader/dataseries.py,sha256=uNv8V7v5RmTrP94WCBdNr7cUBQ96-h1MapA9vsM_TB4,6498
backtrader/errors.py,sha256=h1ztjS5_lwNbH-ESk94sptoekCHYrpc9xqHGu6EMNTg,1890
backtrader/feed.py,sha256=9zUYRgJk06KszE-ardg3pu7Qid6yU8-fThusl6StBTA,26027
backtrader/feeds/__init__.py,sha256=GXPgMnX1lPL7MPEvt-q9qQYAUwqIM6KlHu4IMQmImn8,1674
backtrader/feeds/__pycache__/__init__.cpython-312.pyc,,
backtrader/feeds/__pycache__/blaze.cpython-312.pyc,,
backtrader/feeds/__pycache__/btcsv.cpython-312.pyc,,
backtrader/feeds/__pycache__/chainer.cpython-312.pyc,,
backtrader/feeds/__pycache__/csvgeneric.cpython-312.pyc,,
backtrader/feeds/__pycache__/ibdata.cpython-312.pyc,,
backtrader/feeds/__pycache__/influxfeed.cpython-312.pyc,,
backtrader/feeds/__pycache__/mt4csv.cpython-312.pyc,,
backtrader/feeds/__pycache__/oanda.cpython-312.pyc,,
backtrader/feeds/__pycache__/pandafeed.cpython-312.pyc,,
backtrader/feeds/__pycache__/quandl.cpython-312.pyc,,
backtrader/feeds/__pycache__/rollover.cpython-312.pyc,,
backtrader/feeds/__pycache__/sierrachart.cpython-312.pyc,,
backtrader/feeds/__pycache__/vcdata.cpython-312.pyc,,
backtrader/feeds/__pycache__/vchart.cpython-312.pyc,,
backtrader/feeds/__pycache__/vchartcsv.cpython-312.pyc,,
backtrader/feeds/__pycache__/vchartfile.cpython-312.pyc,,
backtrader/feeds/__pycache__/yahoo.cpython-312.pyc,,
backtrader/feeds/blaze.py,sha256=XouW5xfrVhKX8enRPwQbrN8sB9qej1YrEQltG14D8II,2877
backtrader/feeds/btcsv.py,sha256=vGdJSLcdo8YOv5GkKStl_7ToKAPK4c-SnPMxrbUu3tU,2266
backtrader/feeds/chainer.py,sha256=IVF0fSYy7sL5-BcDe1tXO9_WfvexnD84fIv8bbiaVfY,3489
backtrader/feeds/csvgeneric.py,sha256=T0e1j3k-jXdtiD6c5mI8rIlC8p6DxCpa4Q5Ff4hh9FU,5624
backtrader/feeds/ibdata.py,sha256=r8xeOWQbJ2WmwpxWoH731ez3aJX3FIlcW2t0IAlR8fc,27438
backtrader/feeds/influxfeed.py,sha256=Q2sCoigd-ySJaMc9UUJZxaTye1cSEGjaXFzjfQvUShc,4060
backtrader/feeds/mt4csv.py,sha256=pCPrB4BjO6qm0kmB46PlxGCANQgGjUY9kTNFOiyKhVI,1704
backtrader/feeds/oanda.py,sha256=tqZfLg0uxTHRCZdMze4BSTjj1JEaFFBNMGo_HHfsSKc,16545
backtrader/feeds/pandafeed.py,sha256=bdovxbsznqb05owr9UWX4jg5aVMsI0N8-LBqaQbXP_M,8735
backtrader/feeds/quandl.py,sha256=UnPV3nH5rMW8WUj6eaeGFXLHR3b0RdmXTkRcQvVIK2k,6851
backtrader/feeds/rollover.py,sha256=E8jNV0h9vD9rdkjoIVEplrWoQT8CiZ7iyEl3u_f8CJ0,6892
backtrader/feeds/sierrachart.py,sha256=wzEfMBgeQlzUVyPueT8m_xTvfTXNORZ6w2iCXwDYtXY,1420
backtrader/feeds/vcdata.py,sha256=SW9MOp-UchmoCVxerYO-FgsNwXEzqugKpVqYGYB3SLQ,22443
backtrader/feeds/vchart.py,sha256=XzItoQGrjzFxMmd9CQPwKJlkufiACwDoXeVPQmoddt4,4598
backtrader/feeds/vchartcsv.py,sha256=BLuBdSQPDh6a9iU5jEHOruKMTp-MAYPuZe3n5ivbnzg,2777
backtrader/feeds/vchartfile.py,sha256=JRmN-TfI6aggVNC-nO7_cnL2iVhVYtW1bPCFbeM2D6Y,4662
backtrader/feeds/yahoo.py,sha256=54SrH8Z1sZMYEdY2lzV_oXdYG0lqTU5r1OeNPRuYZ5Y,10867
backtrader/fillers.py,sha256=KDTaWsT7cx1ShZ0EYO0keZyzm3isEhlOFeholOAXa10,3754
backtrader/filters/__init__.py,sha256=yfFb3ctp1cj9ZPfo945b7hwhYAETpWj-HqVHtA-ffYE,1242
backtrader/filters/__pycache__/__init__.cpython-312.pyc,,
backtrader/filters/__pycache__/bsplitter.cpython-312.pyc,,
backtrader/filters/__pycache__/calendardays.cpython-312.pyc,,
backtrader/filters/__pycache__/datafiller.cpython-312.pyc,,
backtrader/filters/__pycache__/datafilter.cpython-312.pyc,,
backtrader/filters/__pycache__/daysteps.cpython-312.pyc,,
backtrader/filters/__pycache__/heikinashi.cpython-312.pyc,,
backtrader/filters/__pycache__/renko.cpython-312.pyc,,
backtrader/filters/__pycache__/session.cpython-312.pyc,,
backtrader/filters/bsplitter.py,sha256=uaZyhyox29cwQW19sidOU0dlgL9KmCMHyI6Ny3p3Fhg,3932
backtrader/filters/calendardays.py,sha256=8IOqFtxeq6JCcdfmT3D5YTbBB7PiYrTeA_GmlR9f2vs,3933
backtrader/filters/datafiller.py,sha256=kKs3Hb7BUHLtuYvVZCHz_a5OoO-xcOyiJLBJHirXAW0,6343
backtrader/filters/datafilter.py,sha256=I52MDj-jVZw0L-V_nUNOkJUpJvN_DhQ9DwEOkOe1-90,2743
backtrader/filters/daysteps.py,sha256=sLenUWqygW0cl-SD638oZErVOOgh9gkBYEi69oSYXCY,3135
backtrader/filters/heikinashi.py,sha256=Yw-ec58KX8rKqJfq1aiQvTPOGcSzU4z4ydnrU3ByRmQ,1944
backtrader/filters/renko.py,sha256=_OxbfVN0yd-_a8cWp-8bTF7UagCZWYNlZtFs_r6N2tQ,4627
backtrader/filters/session.py,sha256=EPeYWZVE-ERI_GvPIrHbLgWBtNQo4N1n1JVuHV9_hNw,8533
backtrader/flt.py,sha256=IQM0ZlAMwVIitqNgq7ZRaxqag85RLp-ZBkzcSKBH_m0,1512
backtrader/functions.py,sha256=DhjvAIZhMVYY5_dcMKiEoTRhF0F7PuYnqhnE1PlZgdw,7219
backtrader/indicator.py,sha256=_j8xj7a7ZVgoGToXg9oXzsPUfzYbHdmxbZPJLfsLkJs,5584
backtrader/indicators/__init__.py,sha256=blJnbOQBF8EgyKZKdr13gHODj3ms4D2nz620s8K4-s8,2548
backtrader/indicators/__pycache__/__init__.cpython-312.pyc,,
backtrader/indicators/__pycache__/accdecoscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/aroon.cpython-312.pyc,,
backtrader/indicators/__pycache__/atr.cpython-312.pyc,,
backtrader/indicators/__pycache__/awesomeoscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/basicops.cpython-312.pyc,,
backtrader/indicators/__pycache__/bollinger.cpython-312.pyc,,
backtrader/indicators/__pycache__/cci.cpython-312.pyc,,
backtrader/indicators/__pycache__/crossover.cpython-312.pyc,,
backtrader/indicators/__pycache__/dema.cpython-312.pyc,,
backtrader/indicators/__pycache__/deviation.cpython-312.pyc,,
backtrader/indicators/__pycache__/directionalmove.cpython-312.pyc,,
backtrader/indicators/__pycache__/dma.cpython-312.pyc,,
backtrader/indicators/__pycache__/dpo.cpython-312.pyc,,
backtrader/indicators/__pycache__/dv2.cpython-312.pyc,,
backtrader/indicators/__pycache__/ema.cpython-312.pyc,,
backtrader/indicators/__pycache__/envelope.cpython-312.pyc,,
backtrader/indicators/__pycache__/hadelta.cpython-312.pyc,,
backtrader/indicators/__pycache__/heikinashi.cpython-312.pyc,,
backtrader/indicators/__pycache__/hma.cpython-312.pyc,,
backtrader/indicators/__pycache__/hurst.cpython-312.pyc,,
backtrader/indicators/__pycache__/ichimoku.cpython-312.pyc,,
backtrader/indicators/__pycache__/kama.cpython-312.pyc,,
backtrader/indicators/__pycache__/kst.cpython-312.pyc,,
backtrader/indicators/__pycache__/lrsi.cpython-312.pyc,,
backtrader/indicators/__pycache__/mabase.cpython-312.pyc,,
backtrader/indicators/__pycache__/macd.cpython-312.pyc,,
backtrader/indicators/__pycache__/momentum.cpython-312.pyc,,
backtrader/indicators/__pycache__/ols.cpython-312.pyc,,
backtrader/indicators/__pycache__/oscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/percentchange.cpython-312.pyc,,
backtrader/indicators/__pycache__/percentrank.cpython-312.pyc,,
backtrader/indicators/__pycache__/pivotpoint.cpython-312.pyc,,
backtrader/indicators/__pycache__/prettygoodoscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/priceoscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/psar.cpython-312.pyc,,
backtrader/indicators/__pycache__/rmi.cpython-312.pyc,,
backtrader/indicators/__pycache__/rsi.cpython-312.pyc,,
backtrader/indicators/__pycache__/sma.cpython-312.pyc,,
backtrader/indicators/__pycache__/smma.cpython-312.pyc,,
backtrader/indicators/__pycache__/stochastic.cpython-312.pyc,,
backtrader/indicators/__pycache__/trix.cpython-312.pyc,,
backtrader/indicators/__pycache__/tsi.cpython-312.pyc,,
backtrader/indicators/__pycache__/ultimateoscillator.cpython-312.pyc,,
backtrader/indicators/__pycache__/vortex.cpython-312.pyc,,
backtrader/indicators/__pycache__/williams.cpython-312.pyc,,
backtrader/indicators/__pycache__/wma.cpython-312.pyc,,
backtrader/indicators/__pycache__/zlema.cpython-312.pyc,,
backtrader/indicators/__pycache__/zlind.cpython-312.pyc,,
backtrader/indicators/accdecoscillator.py,sha256=x6eMfkamEQ6s5anjqW2Vr4-7dZEfvRvYfBdp63LZPW4,2166
backtrader/indicators/aroon.py,sha256=AErUwXyt7gSyvremzVRgyNzI2r0v0CyikgYyhNX2E6o,6378
backtrader/indicators/atr.py,sha256=0epxO3OoTY2qe4GbtVcBESxFMvfzMXCeSnLJ8PO_cXE,3661
backtrader/indicators/awesomeoscillator.py,sha256=k7hwUm5QciQtI4fdqP9GYcIf9TkAqJBWKfOJrBeyoog,2175
backtrader/indicators/basicops.py,sha256=rWkrYs5sj5tGwL1sBRlOVFgQWa4Of5I0RaORLNhXsCQ,13527
backtrader/indicators/bollinger.py,sha256=R4wqE_eGOy1oWq5aykcmvZ0YLaR3hBW60ZwMH-DpbVA,2713
backtrader/indicators/cci.py,sha256=m6BKJrUxweQEohAs2puZVqB7QfAfE0wal0SXYsao_Zg,2416
backtrader/indicators/contrib/__init__.py,sha256=pVQIOd8pbRwGroAsTFESZh_Xj-aaojP0eJVSvinIEAk,1158
backtrader/indicators/contrib/__pycache__/__init__.cpython-312.pyc,,
backtrader/indicators/contrib/__pycache__/vortex.cpython-312.pyc,,
backtrader/indicators/contrib/vortex.py,sha256=u_QuRhChoFY_jeALe6oMUN6LFNuIeWVpgK3msZFdWI4,1953
backtrader/indicators/crossover.py,sha256=CqE_y1H4arTPAqpz6iZQDsjUbMEm8B2Ro21IMNF8CdU,4245
backtrader/indicators/dema.py,sha256=Q6rhcVP0Xzi_nv51Cif0YgXqcRdquV4ufYisHWtqxyM,2843
backtrader/indicators/deviation.py,sha256=oA1UuZRhlT1ur_zJmPrWQGAn96yfC3BPOteAHltm95k,3596
backtrader/indicators/directionalmove.py,sha256=quRoJ5uCcaF_G18Az2isnGgU5TQovV_jy4-Q6ysTEWc,13343
backtrader/indicators/dma.py,sha256=es8aywgQnVgo8VpV0b5f9z3XEzOMIVwkkx01OwKQnQ8,2873
backtrader/indicators/dpo.py,sha256=PXd00-G6xdhUGnbHOxrtDGrTedh369p0c04tjin_URA,2401
backtrader/indicators/dv2.py,sha256=pRiFo_cNbk6a4BTh3sbcAdhS-nQOURJPhOdk593vZ4w,1781
backtrader/indicators/ema.py,sha256=c5iFPVs7cCpBuJiDfNpI-E9KLTrPURdO0uWw68W2GP0,2025
backtrader/indicators/envelope.py,sha256=OyJPyaxVOthhMsdhklxEVQj60meuiDKXPNzTNNpezng,3996
backtrader/indicators/hadelta.py,sha256=IfkxnGDxVCh74qEViScHueKlgPviTuwKir82j9uKJOM,2282
backtrader/indicators/heikinashi.py,sha256=qKVtMztN4DrE9n9PE8etYL2mL_yB3mS0mlXACa9Qtz4,2419
backtrader/indicators/hma.py,sha256=uOL9m87mAKdIzq808Art826l3Nx4jOl0KZqjHESGRNI,2579
backtrader/indicators/hurst.py,sha256=ZjT6sbe8Rdaxg2ebPDH2psglK47B-tWdzaaOkx-VumY,3307
backtrader/indicators/ichimoku.py,sha256=eNq5v8ns_LjVMovHyabxx8oByamHwIG_oGJqgCKzfLA,3138
backtrader/indicators/kama.py,sha256=_9SIl2pHmiXGFrGua6OHmDZrrknvQJZEVM2MYfV0sn0,3378
backtrader/indicators/kst.py,sha256=aVrnM1mNb3cEGwhDa297BT9vGu_xxPsimTJoYp-OQ0c,3008
backtrader/indicators/lrsi.py,sha256=xL0_USOzpHLE-eufCd2jLI6scOeNn1ZOeDo3Udo4Vx4,3556
backtrader/indicators/mabase.py,sha256=AlBbBbVLcb2fZRW83x4Ic881k_5qu-Ev9RqRRkiVs9A,2719
backtrader/indicators/macd.py,sha256=zr3XHDik91ruv9j_s5Ze3jpoy3_FQpfP4CX0ocKQ07w,2837
backtrader/indicators/momentum.py,sha256=6Sa4AOqhtPGZlQaE4LHRBLsKQghXyqch8bG5SjKfMnI,3525
backtrader/indicators/ols.py,sha256=I0ignLsA1wwxET-9bFhVP3U83Jruqc6Cfcwc-_sPTrY,3987
backtrader/indicators/oscillator.py,sha256=kjxqzkk6d6S6DqHiWxGG_PZ8FoJJty2zpf6EAm3UWnA,4050
backtrader/indicators/percentchange.py,sha256=Qn5hEjNkXpkclzmILmcFbnc_gFXdvFc893vNliLFIjA,1605
backtrader/indicators/percentrank.py,sha256=GFSOPY99cuRfialQ_NTQo5aBO-tlHVCS6lkIY4xI5fk,1410
backtrader/indicators/pivotpoint.py,sha256=OLrDycJ3XntTMZCpbLwaNQ9MDN4gR1v9FGygNTHueqY,9353
backtrader/indicators/prettygoodoscillator.py,sha256=cxGtt9All6UjTdnm3_Q-FamOkZ8Q1lhFaIxUw8KF0FA,2262
backtrader/indicators/priceoscillator.py,sha256=Uqdv3tjbM0Mb4pernpJAO9uLUbrGYXF--7PhpjhS3m0,3801
backtrader/indicators/psar.py,sha256=kfRNmPLJq5f6XZ87LgnWqN4Q9q07SGKowWa2YOoKEr0,5931
backtrader/indicators/rmi.py,sha256=RMZy_H0-G20jM8DcifJEl0aL9yBrhvVQ-tcajA3vuB8,2474
backtrader/indicators/rsi.py,sha256=i52wf07GI-zr7LNCKB3tEec7LT0TT0rJODCcPremfvs,6813
backtrader/indicators/sma.py,sha256=VXTNN61s6aGY2LaK3FUhGBxByxUp9MrJkYhwyfdZLPA,1665
backtrader/indicators/smma.py,sha256=BwRPn3UjRCMdoYasVe6tUCtbW89TDF59AtE5FipFY6A,2190
backtrader/indicators/stochastic.py,sha256=56V878HzybMTY45GntBXbpAmT1QCwjZUIlM9aFFJ0v8,4869
backtrader/indicators/trix.py,sha256=EWpsbO3fvcmxhaGiiquSwpsKZB-smgM83156NEp3RyE,2947
backtrader/indicators/tsi.py,sha256=qkxpamXTeOirkiaKkbk9motinE41TJJqO47blqJELkg,2660
backtrader/indicators/ultimateoscillator.py,sha256=hCSBORhhG8HmhVfo65rjV6TAd1HrMT_4_A0a4MX2f_g,2913
backtrader/indicators/vortex.py,sha256=U4VPPaDyQtU3TAlDFT0vcE2SdLBv-fhP6_S_4NKZjMs,1888
backtrader/indicators/williams.py,sha256=N1CVx_QU0s2CyPIb5n0YsTIALNgAtcvzJrL8X7tWfz0,3038
backtrader/indicators/wma.py,sha256=pJDjAuhZQ1_bz4o-D5X9HlljrW4ySf11QKyLOp_4UCo,2088
backtrader/indicators/zlema.py,sha256=JRv7sc7zC3XXhgc1CDgR7cyawvGiWz6yH-XeFYJXYV4,1880
backtrader/indicators/zlind.py,sha256=_eIdCxld9tUlmSr1oAFgDcmvEnpBDrgBhgW_jfJNtwY,3192
backtrader/linebuffer.py,sha256=Msu8bU6eQVC_uwIW_PqWCdceOnsSu0JcWj6yth-fTqE,26583
backtrader/lineiterator.py,sha256=T9ZBhmZY4ps7oJGrb46Gi02Xy0ltlPftRvToi3RIQLU,15838
backtrader/lineroot.py,sha256=JUmTKhgaZ8GzakYFicpv_Qiafi04E7C5KeVGjK1ZqFs,10689
backtrader/lineseries.py,sha256=3WIDwcAD6qLiaRfjL76mwSztmDsL1Z7pIZuCj3rfMC4,22007
backtrader/mathsupport.py,sha256=jc2fTjaf3dWzQ4ZV-ypvVPrHnKnoJbrWLHd-pIhdQ2w,1923
backtrader/metabase.py,sha256=3TkPMWkS3fg-pRFeCmxh81tqC1nS613aa2M0iRQH-MA,11160
backtrader/observer.py,sha256=o2-HnrP5pb918CpHTznskCBe1P8DnB_tdjjhqDCmXg4,2274
backtrader/observers/__init__.py,sha256=tRs9w-FOKBCc5Ez8k4TEmqL1_-rF6MhEoJU5lu7IOkw,1325
backtrader/observers/__pycache__/__init__.cpython-312.pyc,,
backtrader/observers/__pycache__/benchmark.cpython-312.pyc,,
backtrader/observers/__pycache__/broker.cpython-312.pyc,,
backtrader/observers/__pycache__/buysell.cpython-312.pyc,,
backtrader/observers/__pycache__/drawdown.cpython-312.pyc,,
backtrader/observers/__pycache__/logreturns.cpython-312.pyc,,
backtrader/observers/__pycache__/timereturn.cpython-312.pyc,,
backtrader/observers/__pycache__/trades.cpython-312.pyc,,
backtrader/observers/benchmark.py,sha256=cfRmgiam9AVUx32xFUJuNSSp7ht79JOORXVGojJKzAw,4314
backtrader/observers/broker.py,sha256=NJm0GRQOSYitwIzjTpAuqAR4c5t-e2UZtFBgHa3FmCk,3868
backtrader/observers/buysell.py,sha256=t9UZgHGmLsbyMYVLcVtjlTXOc31wyrRhl8tx93CqF1s,3882
backtrader/observers/drawdown.py,sha256=FO_zmFd9G5t2oMVQX78naPq57LnMiyZModV1t81yB0k,3643
backtrader/observers/logreturns.py,sha256=cUjNFoly2-hGANz3Py0LGSau8VRgXEBrZ4odXx3h-Lc,3234
backtrader/observers/timereturn.py,sha256=2QRyx5lDpuPVPPJ_q8HEWDpZ8aMA1afsJz5YThmcKVo,3088
backtrader/observers/trades.py,sha256=4nRFo5PSIpKZ2kiJSrktvXZtP3-oU6_90SFGC9yyRwY,4983
backtrader/order.py,sha256=saiCfenEDSXpMrxkRVJ5oJuLY4m2gaAZMQ3uaOZP_10,21937
backtrader/plot/__init__.py,sha256=N0Uk24eUMPgEzr1-6dGYaOFaSZnXoEXrEx7pJRmAr8Q,1494
backtrader/plot/__pycache__/__init__.cpython-312.pyc,,
backtrader/plot/__pycache__/finance.cpython-312.pyc,,
backtrader/plot/__pycache__/formatters.cpython-312.pyc,,
backtrader/plot/__pycache__/locator.cpython-312.pyc,,
backtrader/plot/__pycache__/multicursor.cpython-312.pyc,,
backtrader/plot/__pycache__/plot.cpython-312.pyc,,
backtrader/plot/__pycache__/scheme.cpython-312.pyc,,
backtrader/plot/__pycache__/utils.cpython-312.pyc,,
backtrader/plot/finance.py,sha256=FfTm0tmO9XaguQtajcpfOSAAV5YaJlhhSvxaXip_J-w,19346
backtrader/plot/formatters.py,sha256=_YxtIdagngspBXg-KfdVU5NHmD91Idlvsnz4ooKbJhA,4042
backtrader/plot/locator.py,sha256=kiy9HQRninw2rOJM72HNBKjr0S669RRYHebYzGcoOYA,9435
backtrader/plot/multicursor.py,sha256=4fF2BmG-hx97xdw86WKnzDTNknhS43W5phHHV_jfrRY,12231
backtrader/plot/plot.py,sha256=9Lk9-6Gw9MgOMPKSP_XHOaAeAUSPD9PhPVo8HiS_zN0,33959
backtrader/plot/scheme.py,sha256=VPNOAnoZEQkKTpim-GIG4IgaFDextxsu9Qo0brhGTiQ,6433
backtrader/plot/utils.py,sha256=b9mUce7_0Q5Sp0Zz4ogoocxouc8VlOJxc8HkRLY5vak,2920
backtrader/position.py,sha256=odBPNNkWVRHZnNY03SqP0TSlhP1I8xoCMHJCuS2rH7E,7445
backtrader/resamplerfilter.py,sha256=z0hqTqbj4nj7gWQPR9qtBk9Iqz8wcqgF3iqL04cMJdA,26141
backtrader/signal.py,sha256=2JIr-igZ-W70QEfv3zqAsLkI3tVRjHZ9TwkK8DtKs_M,1894
backtrader/signals/__init__.py,sha256=k6xw7r6DXnh0Rrk37YY_Lf1jGCWLdBeaMF99OkEvyXw,1019
backtrader/signals/__pycache__/__init__.cpython-312.pyc,,
backtrader/sizer.py,sha256=7uf6kzcoD5ko4Rjo9L95e33rq8Dl8t5FE2Rd4FOQxF4,2977
backtrader/sizers/__init__.py,sha256=m2LNrWaXw1__hSr2W6a8VhD1Bu7kEO8QF_9pjZKSRlI,1208
backtrader/sizers/__pycache__/__init__.cpython-312.pyc,,
backtrader/sizers/__pycache__/fixedsize.cpython-312.pyc,,
backtrader/sizers/__pycache__/percents_sizer.cpython-312.pyc,,
backtrader/sizers/fixedsize.py,sha256=8nGDwqcCA8NFZUEWxpFsE0rLDDEEc-hucIUHOFveDHw,3486
backtrader/sizers/percents_sizer.py,sha256=AF3TvbFVhUmDhisDcACUKYZv5mCLD7I9t4q49zS8hwU,2482
backtrader/store.py,sha256=vg4Fv8ltjHy_jZuIAWhKPVUb2VPvdVTdpm8ENv0Pxjo,3135
backtrader/stores/__init__.py,sha256=tEIkB8m-ZDOR5_6cU_nfDcL111PPWtuFp8fifDYbRCQ,1527
backtrader/stores/__pycache__/__init__.cpython-312.pyc,,
backtrader/stores/__pycache__/ibstore.cpython-312.pyc,,
backtrader/stores/__pycache__/oandastore.cpython-312.pyc,,
backtrader/stores/__pycache__/vchartfile.cpython-312.pyc,,
backtrader/stores/__pycache__/vcstore.cpython-312.pyc,,
backtrader/stores/ibstore.py,sha256=QS2bnENe6V3k3HvCs-GRvM5ND_nd7IR1gfLNh_veNCA,54280
backtrader/stores/oandastore.py,sha256=eMUbywmqwOV6AK1JHPj2mAgOOGqVUTPFTitfYOSh34g,22120
backtrader/stores/vchartfile.py,sha256=5Rq0N7tsujeLSq3GO61diuiL8KVBieIsLFOAcG3pwM0,2700
backtrader/stores/vcstore.py,sha256=i3HjzSaHCBxUjsWnPh2mSSxHCKSCLy7u9-jT8SH-1wc,19039
backtrader/strategies/__init__.py,sha256=cmvtZgWgXhy68eB8g6L89BUjGlKJVf7yn7qZ5oNUpCs,1048
backtrader/strategies/__pycache__/__init__.cpython-312.pyc,,
backtrader/strategies/__pycache__/sma_crossover.cpython-312.pyc,,
backtrader/strategies/sma_crossover.py,sha256=Unx4TO2Hb1XkuWbtOfc_GMliUys93dN5UlMkDlefBYU,2197
backtrader/strategy.py,sha256=ilbrwdell8QfIL012DsH_ekNpg31C-RrP8O6uFqyHRg,61718
backtrader/studies/__init__.py,sha256=P5fublCLg_K8idOxV24SzMgMKCrvsqBCRQhQOtzhHvQ,1053
backtrader/studies/__pycache__/__init__.cpython-312.pyc,,
backtrader/studies/contrib/__init__.py,sha256=LBZiqyy2G6iL19ADffQQuKb_KygNSt3TPjQGNOa04YE,1159
backtrader/studies/contrib/__pycache__/__init__.cpython-312.pyc,,
backtrader/studies/contrib/__pycache__/fractal.cpython-312.pyc,,
backtrader/studies/contrib/fractal.py,sha256=MVVp4oP4-kgmkr6wp9zenkRot280ZCHICqEFuiTuI0o,2608
backtrader/talib.py,sha256=0TnyB1RkwyE0N3Af2jzIeqP-wqyErlMVz1M2-TNWNHw,8950
backtrader/timer.py,sha256=kl9MKjWufhiyw6ndpMTT5XJIyW-cp8MI-QPm5NrjdgU,7585
backtrader/trade.py,sha256=7f5CM0_2Q6bPpZi8j1qRZbrnZLcT0cpxr2oiiYWeb9c,11662
backtrader/tradingcal.py,sha256=b1izVsugrNaWHIkuERpjYXDvxbG2c4gm1jq7M5D0_PU,9893
backtrader/utils/__init__.py,sha256=TYPfz-M8SfXCQ2wieR-3Qz6jSvdVa09ONRvhftaG5EE,1145
backtrader/utils/__pycache__/__init__.cpython-312.pyc,,
backtrader/utils/__pycache__/autodict.cpython-312.pyc,,
backtrader/utils/__pycache__/date.cpython-312.pyc,,
backtrader/utils/__pycache__/dateintern.cpython-312.pyc,,
backtrader/utils/__pycache__/flushfile.cpython-312.pyc,,
backtrader/utils/__pycache__/ordereddefaultdict.cpython-312.pyc,,
backtrader/utils/__pycache__/py3.cpython-312.pyc,,
backtrader/utils/autodict.py,sha256=OKQCZfUkjJ1VFwIPDQ5PX7J1Fsi-_7SHzk6qW5Jvv2o,3787
backtrader/utils/date.py,sha256=ibOaa7Y2ckvIpdjcRpDnm1odalnnh_UZpPYB2fKdHGY,1319
backtrader/utils/dateintern.py,sha256=BIT4fdulFC-L5DeooTrL1I1fCXO7VqwbvrL4izIRXBE,6972
backtrader/utils/flushfile.py,sha256=ekrNdFCs0j4HwRRxho-Fnl0XZBCrpd0uHyq02kV2wjE,1588
backtrader/utils/ordereddefaultdict.py,sha256=8y7FDnqI1mTcr1NwVfgqsaK-42XwC76Mu_iYRVvLmLM,2091
backtrader/utils/py3.py,sha256=AcIU2yJGqwiu_v0wOqhf95NnXpI-4aYPpzpyOFG2FbA,3409
backtrader/version.py,sha256=xdzgzzR92xxXIE4dyVkyS0B8pbENoMpat61adXNxSbk,1110
backtrader/writer.py,sha256=SGrqtTWFnEdIVHrV7F2cr9CdKRjdPJhIY13Gppm6B50,7741
