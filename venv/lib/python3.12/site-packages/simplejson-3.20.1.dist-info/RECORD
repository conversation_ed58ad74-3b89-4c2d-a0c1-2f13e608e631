simplejson-3.20.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
simplejson-3.20.1.dist-info/LICENSE.txt,sha256=_KoeMTqZMEMi4uCDbqTufYOc6aSAQ485IqhT7f3eEp8,10375
simplejson-3.20.1.dist-info/METADATA,sha256=YrXCM4tahy43t5THZ6eJuaBJhmRoJlIOzyLaiqAcUb4,3328
simplejson-3.20.1.dist-info/RECORD,,
simplejson-3.20.1.dist-info/WHEEL,sha256=tB43aRiqo0xFZG1lo9A8oCVJCT-c6TfvIAC6bY0A8ec,110
simplejson-3.20.1.dist-info/top_level.txt,sha256=ohU816e0ltfT8aBFol4_XY1q9GBh9tEBbcPpNn49200,11
simplejson/__init__.py,sha256=TVmnBn3yAe9XjEQTnxCa7BDZnjSfaKS06QR4y0sFcZg,23497
simplejson/__pycache__/__init__.cpython-312.pyc,,
simplejson/__pycache__/compat.cpython-312.pyc,,
simplejson/__pycache__/decoder.cpython-312.pyc,,
simplejson/__pycache__/encoder.cpython-312.pyc,,
simplejson/__pycache__/errors.cpython-312.pyc,,
simplejson/__pycache__/ordered_dict.cpython-312.pyc,,
simplejson/__pycache__/raw_json.cpython-312.pyc,,
simplejson/__pycache__/scanner.cpython-312.pyc,,
simplejson/__pycache__/tool.cpython-312.pyc,,
simplejson/_speedups.cpython-312-darwin.so,sha256=KuVJDd7MnhH6f7QTxYjj7Dflznku1lgE5vHJunp7ffg,47536
simplejson/compat.py,sha256=lroo216GraRWaBbohPWrlO_0YCOaHSFK1jjCCcV62lg,815
simplejson/decoder.py,sha256=aua7gXXntwQNAwH2zJtd4CdAZC2RDSUiCIfFfRn8v9w,15133
simplejson/encoder.py,sha256=O_HhBi1e8Z2ZiwnaIKSavIjU-TUjctPgbeDkeTXAp_s,29093
simplejson/errors.py,sha256=aoN2BsYYl8xNypq3U7ePA0hvTb6PjCGvFxZ48gt_blo,1779
simplejson/ordered_dict.py,sha256=RCG89gd0AHPhpGd3Hy8fF_Mz4PcpvE0PJ_vTMfVEVbw,2945
simplejson/raw_json.py,sha256=Gru3JsbV9lDBWjfmw6s24toi_s2M7-juyjEwrNzdgHg,217
simplejson/scanner.py,sha256=IXLRkWH9RhmM8wntlpedb_XybLXoUYNI_tVbCZYUe1Y,3028
simplejson/tests/__init__.py,sha256=xfC3DcLSin-HdC0pW7d48QR5YwW5iOTbBeXFibaDfAQ,2512
simplejson/tests/__pycache__/__init__.cpython-312.pyc,,
simplejson/tests/__pycache__/_cibw_runner.cpython-312.pyc,,
simplejson/tests/__pycache__/test_bigint_as_string.cpython-312.pyc,,
simplejson/tests/__pycache__/test_bitsize_int_as_string.cpython-312.pyc,,
simplejson/tests/__pycache__/test_check_circular.cpython-312.pyc,,
simplejson/tests/__pycache__/test_decimal.cpython-312.pyc,,
simplejson/tests/__pycache__/test_decode.cpython-312.pyc,,
simplejson/tests/__pycache__/test_default.cpython-312.pyc,,
simplejson/tests/__pycache__/test_dump.cpython-312.pyc,,
simplejson/tests/__pycache__/test_encode_basestring_ascii.cpython-312.pyc,,
simplejson/tests/__pycache__/test_encode_for_html.cpython-312.pyc,,
simplejson/tests/__pycache__/test_errors.cpython-312.pyc,,
simplejson/tests/__pycache__/test_fail.cpython-312.pyc,,
simplejson/tests/__pycache__/test_float.cpython-312.pyc,,
simplejson/tests/__pycache__/test_for_json.cpython-312.pyc,,
simplejson/tests/__pycache__/test_indent.cpython-312.pyc,,
simplejson/tests/__pycache__/test_item_sort_key.cpython-312.pyc,,
simplejson/tests/__pycache__/test_iterable.cpython-312.pyc,,
simplejson/tests/__pycache__/test_namedtuple.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass1.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass2.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass3.cpython-312.pyc,,
simplejson/tests/__pycache__/test_raw_json.cpython-312.pyc,,
simplejson/tests/__pycache__/test_recursion.cpython-312.pyc,,
simplejson/tests/__pycache__/test_scanstring.cpython-312.pyc,,
simplejson/tests/__pycache__/test_separators.cpython-312.pyc,,
simplejson/tests/__pycache__/test_speedups.cpython-312.pyc,,
simplejson/tests/__pycache__/test_str_subclass.cpython-312.pyc,,
simplejson/tests/__pycache__/test_subclass.cpython-312.pyc,,
simplejson/tests/__pycache__/test_tool.cpython-312.pyc,,
simplejson/tests/__pycache__/test_tuple.cpython-312.pyc,,
simplejson/tests/__pycache__/test_unicode.cpython-312.pyc,,
simplejson/tests/_cibw_runner.py,sha256=-QiXXWHKxWQN5YeRg6m-2jpeqwh23Wo0G1AmhHBa2cg,173
simplejson/tests/test_bigint_as_string.py,sha256=YOmI4Kq6GaSjYDvsKZATZwScWGjy6UH7JHR1_r-pzbo,2238
simplejson/tests/test_bitsize_int_as_string.py,sha256=qkJ4TDMPKMU5z4ZBZWJhx-lnZupHieHPMy0rjvpBchM,2297
simplejson/tests/test_check_circular.py,sha256=coR4CialB3LXQYIC76WRCdY3kCc7d_PN4pikRvFwYLI,917
simplejson/tests/test_decimal.py,sha256=AeF0xULB9vpq2zk-QvV4rrJ5mMCr8RVvK3F83_gF8MM,2544
simplejson/tests/test_decode.py,sha256=2mUmMJhFyGOW3ck3zQgEOuRgJx21rQiMyP6aMpnrPaU,5181
simplejson/tests/test_default.py,sha256=f5f4SdHOt9P9VPobIaCj1zjMAxDLV0xmPDyba_s3MgU,221
simplejson/tests/test_dump.py,sha256=MV8NB24gSyhSseFFeRBzJMRNocnP0VC-PmfpHFJ8iVM,10570
simplejson/tests/test_encode_basestring_ascii.py,sha256=e0xEaTjYvDmxhuq6oKkcZH-xWm6yX1WSUBn3SQNFoZ4,2337
simplejson/tests/test_encode_for_html.py,sha256=SD6f_v1HlXo8B3BhkstH1okAnK674xO7xM7eWuvBZCo,1515
simplejson/tests/test_errors.py,sha256=ejUZjquM0pCV5zDoM7s3DJk9prXaWKXyiNqKrlYRbMk,2081
simplejson/tests/test_fail.py,sha256=qFb27hTYltxHWNyVkUV7hhHWvDb4iwUoUvKuXMZQ2qc,6454
simplejson/tests/test_float.py,sha256=FckoWqDnSUzwIN9gnuXG_9_SrccX7RcK4Oun4YtFqZI,1676
simplejson/tests/test_for_json.py,sha256=1tOVgNq3jNF6RiSwPXZsL2LAzn7F41DzjiGB-qXSHEI,2767
simplejson/tests/test_indent.py,sha256=Hjq07hCCJdn_rCgFZQQo_1asyrEwblGGd_iDT9VwT7M,2568
simplejson/tests/test_item_sort_key.py,sha256=g8z0oTPEmLZU3-50UQpjkUTM625PnE8sKrtfA9wNSE0,1376
simplejson/tests/test_iterable.py,sha256=EXJX_86BOIXbBDxWnsNYWu617dSZgI0E6MePB2Z-m8I,1390
simplejson/tests/test_namedtuple.py,sha256=ZzuDiuQ0lP9uMNAM2cMfSV2BcvKxWcXyhheyc7_DgFc,5896
simplejson/tests/test_pass1.py,sha256=xd0cUaX9W5NSv0QXzca4YY2dlguh7oVu2ohB--o17bE,1746
simplejson/tests/test_pass2.py,sha256=WC5Jf_9maIYHbY4oXaz48QRj9bsqPyO4Kk6uza1NQN8,386
simplejson/tests/test_pass3.py,sha256=hIgmzLQ0__oh3nk7LVU1evB_capjo5gyp3NNtVKDkig,482
simplejson/tests/test_raw_json.py,sha256=JbFWo6rXPXxGp5AEEkZlajaZ4qml3dYlDOLPKdULr20,1062
simplejson/tests/test_recursion.py,sha256=XDqZDpIdvaUD-DyrPb6lJcWEXuLIix67LjdFQg3P_PA,1679
simplejson/tests/test_scanstring.py,sha256=qN07H4enZ29Dc3zjKrSJuK8T6LYcYfO8BIISEqbuU5Q,7648
simplejson/tests/test_separators.py,sha256=dU3qO2WizLbmOUT6qxr4kYrR8v8baOOXjW4qRFrYMW4,942
simplejson/tests/test_speedups.py,sha256=Votv1dam3TKjJad3ZGkfTi8vbz1iU_azSmmaQoZ8rPA,4144
simplejson/tests/test_str_subclass.py,sha256=cdvtE7Lup5vkgPFuaPou75dHzPtF5yyWVhptADsSA-Y,740
simplejson/tests/test_subclass.py,sha256=XlOYkwK57q_Z5VWZGIdIrB_gAHxSvQbg_8m5kAnWMAI,1124
simplejson/tests/test_tool.py,sha256=Rf791R1ukpPFvcpm_dFPT_2eoLEwu4sNpsolezMeqzo,3304
simplejson/tests/test_tuple.py,sha256=fJlHB4DDDlMnY6uOo60GmU14vVAYcKWJQ3BcQqXJpNM,1831
simplejson/tests/test_unicode.py,sha256=cKLGLj2egCqi_7xB_pV-mVaB5EQoveAUIDKHlCLOlZo,7056
simplejson/tool.py,sha256=Qoi3LNTtCy0ABALI4ngFuFMyjyIKMpcCNRlkgLgQN_0,1136
