from collections.abc import Iterator, Mapping
from matplotlib import colors
from matplotlib.colorizer import _ScalarMappable


class ColormapRegistry(Mapping[str, colors.Colormap]):
    def __init__(self, cmaps: Mapping[str, colors.Colormap]) -> None: ...
    def __getitem__(self, item: str) -> colors.Colormap: ...
    def __iter__(self) -> Iterator[str]: ...
    def __len__(self) -> int: ...
    def __call__(self) -> list[str]: ...
    def register(
        self, cmap: colors.Colormap, *, name: str | None = ..., force: bool = ...
    ) -> None: ...
    def unregister(self, name: str) -> None: ...
    def get_cmap(self, cmap: str | colors.Colormap) -> colors.Colormap: ...

_colormaps: ColormapRegistry = ...
_multivar_colormaps: ColormapRegistry = ...
_bivar_colormaps: ColormapRegistry = ...

def get_cmap(name: str | colors.Colormap | None = ..., lut: int | None = ...) -> colors.Colormap: ...

ScalarMappable = _ScalarMappable
