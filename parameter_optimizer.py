#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略参数优化工具
"""

import backtrader as bt
import pandas as pd
import numpy as np
from itertools import product
from local_data_manager import LocalDataManager
from data_config import config
from optimized_dual_ma_strategy import OptimizedDualMAStrategy


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, symbol, start_date, end_date):
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        
        # 获取数据
        manager = LocalDataManager(
            db_path=config.get_database_path(),
            tushare_token=config.get_tushare_token()
        )
        self.data = manager.get_data(symbol, start_date, end_date)
        
        if self.data is None or self.data.empty:
            raise ValueError(f"无法获取 {symbol} 的数据")
    
    def optimize_ma_periods(self):
        """优化移动平均线周期"""
        print("=" * 60)
        print("移动平均线周期优化")
        print("=" * 60)
        
        # 参数范围
        fast_periods = [5, 8, 10, 12, 15]
        slow_periods = [20, 25, 30, 35, 40, 50]
        
        results = []
        
        for fast, slow in product(fast_periods, slow_periods):
            if fast >= slow:  # 快线必须小于慢线
                continue
            
            try:
                result = self._run_single_test(
                    fast_period=fast,
                    slow_period=slow,
                    stop_loss=0.08,
                    take_profit=0.15
                )
                
                if result:
                    results.append({
                        'fast_period': fast,
                        'slow_period': slow,
                        'sharpe': result['sharpe'],
                        'max_drawdown': result['max_drawdown'],
                        'total_return': result['total_return'],
                        'win_rate': result['win_rate'],
                        'profit_loss_ratio': result['profit_loss_ratio']
                    })
                    
                    print(f"MA({fast},{slow}): 夏普{result['sharpe']:.3f}, "
                          f"回撤{result['max_drawdown']:.1f}%, "
                          f"收益{result['total_return']:.1f}%")
                          
            except Exception as e:
                print(f"MA({fast},{slow}): 测试失败 - {e}")
        
        if results:
            df = pd.DataFrame(results)
            
            # 按夏普比率排序
            df_sorted = df.sort_values('sharpe', ascending=False)
            
            print(f"\n最佳移动平均线组合 (按夏普比率):")
            print(df_sorted.head().to_string(index=False))
            
            return df_sorted.iloc[0].to_dict()
        
        return None
    
    def optimize_risk_params(self, best_ma_params=None):
        """优化风险控制参数"""
        print("\n" + "=" * 60)
        print("风险控制参数优化")
        print("=" * 60)
        
        # 使用最佳MA参数或默认值
        fast_period = best_ma_params['fast_period'] if best_ma_params else 10
        slow_period = best_ma_params['slow_period'] if best_ma_params else 30
        
        # 风险参数范围
        stop_losses = [0.05, 0.08, 0.10, 0.12, 0.15]
        take_profits = [0.10, 0.15, 0.20, 0.25, 0.30]
        
        results = []
        
        for stop_loss, take_profit in product(stop_losses, take_profits):
            if take_profit <= stop_loss:  # 止盈必须大于止损
                continue
            
            try:
                result = self._run_single_test(
                    fast_period=fast_period,
                    slow_period=slow_period,
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                
                if result:
                    results.append({
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'sharpe': result['sharpe'],
                        'max_drawdown': result['max_drawdown'],
                        'total_return': result['total_return'],
                        'win_rate': result['win_rate'],
                        'profit_loss_ratio': result['profit_loss_ratio']
                    })
                    
                    print(f"止损{stop_loss*100:.0f}%/止盈{take_profit*100:.0f}%: "
                          f"夏普{result['sharpe']:.3f}, "
                          f"回撤{result['max_drawdown']:.1f}%")
                          
            except Exception as e:
                print(f"止损{stop_loss}/止盈{take_profit}: 测试失败 - {e}")
        
        if results:
            df = pd.DataFrame(results)
            
            # 综合评分：夏普比率权重0.4，回撤权重0.6
            df['score'] = df['sharpe'] * 0.4 - (df['max_drawdown'] / 100) * 0.6
            df_sorted = df.sort_values('score', ascending=False)
            
            print(f"\n最佳风险控制参数 (综合评分):")
            print(df_sorted.head().to_string(index=False))
            
            return df_sorted.iloc[0].to_dict()
        
        return None
    
    def _run_single_test(self, **params):
        """运行单次回测"""
        cerebro = bt.Cerebro()
        
        # 添加策略
        cerebro.addstrategy(OptimizedDualMAStrategy,
                           printlog=False,  # 关闭日志
                           **params)
        
        # 添加数据
        data_feed = bt.feeds.PandasData(dataname=self.data)
        cerebro.adddata(data_feed)
        
        # 设置资金和手续费
        cerebro.broker.setcash(10000)
        cerebro.broker.setcommission(commission=0.001)
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        
        # 运行回测
        initial_value = cerebro.broker.getvalue()
        results = cerebro.run()
        final_value = cerebro.broker.getvalue()
        
        if not results:
            return None
        
        strat = results[0]
        
        # 提取指标
        try:
            # 夏普比率
            sharpe_analysis = strat.analyzers.sharpe.get_analysis()
            sharpe = sharpe_analysis.get('sharperatio', 0) or 0
            
            # 最大回撤
            drawdown_analysis = strat.analyzers.drawdown.get_analysis()
            max_drawdown = drawdown_analysis.get('max', {}).get('drawdown', 0) or 0
            
            # 总收益率
            total_return = (final_value - initial_value) / initial_value * 100
            
            # 交易统计
            trades_analysis = strat.analyzers.trades.get_analysis()
            total_trades = trades_analysis.get('total', {}).get('total', 0) or 0
            won_trades = trades_analysis.get('won', {}).get('total', 0) or 0
            
            win_rate = (won_trades / total_trades * 100) if total_trades > 0 else 0
            
            # 盈亏比
            avg_win = trades_analysis.get('won', {}).get('pnl', {}).get('average', 0) or 0
            avg_loss = trades_analysis.get('lost', {}).get('pnl', {}).get('average', 0) or 0
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
            
            return {
                'sharpe': sharpe,
                'max_drawdown': max_drawdown,
                'total_return': total_return,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'total_trades': total_trades
            }
            
        except Exception as e:
            print(f"分析结果提取失败: {e}")
            return None
    
    def run_full_optimization(self):
        """运行完整优化流程"""
        print(f"开始优化 {self.symbol} 策略参数...")
        
        # 1. 优化移动平均线参数
        best_ma = self.optimize_ma_periods()
        
        # 2. 优化风险控制参数
        best_risk = self.optimize_risk_params(best_ma)
        
        # 3. 综合最佳参数
        if best_ma and best_risk:
            optimal_params = {
                'fast_period': int(best_ma['fast_period']),
                'slow_period': int(best_ma['slow_period']),
                'stop_loss': best_risk['stop_loss'],
                'take_profit': best_risk['take_profit']
            }
            
            print(f"\n" + "=" * 60)
            print("最终优化结果")
            print("=" * 60)
            print(f"最佳参数组合:")
            for key, value in optimal_params.items():
                print(f"  {key}: {value}")
            
            # 运行最终测试
            print(f"\n最终回测结果:")
            final_result = self._run_single_test(**optimal_params)
            if final_result:
                print(f"  夏普比率: {final_result['sharpe']:.3f}")
                print(f"  最大回撤: {final_result['max_drawdown']:.2f}%")
                print(f"  总收益率: {final_result['total_return']:.2f}%")
                print(f"  胜率: {final_result['win_rate']:.2f}%")
                print(f"  盈亏比: {final_result['profit_loss_ratio']:.2f}")
            
            return optimal_params
        
        return None


def optimize_strategy(symbol='300059'):
    """优化策略参数"""
    try:
        optimizer = ParameterOptimizer(
            symbol=symbol,
            start_date='2020-01-01',
            end_date='2025-01-01'
        )
        
        optimal_params = optimizer.run_full_optimization()
        
        if optimal_params:
            print(f"\n🎯 为 {symbol} 找到最佳参数!")
            return optimal_params
        else:
            print(f"\n❌ {symbol} 参数优化失败")
            return None
            
    except Exception as e:
        print(f"优化过程出错: {e}")
        return None


if __name__ == '__main__':
    # 运行参数优化
    optimal_params = optimize_strategy('300059')
    
    if optimal_params:
        print(f"\n建议使用以下参数运行优化策略:")
        print(f"python optimized_dual_ma_strategy.py")
        print(f"然后手动修改参数为: {optimal_params}")
