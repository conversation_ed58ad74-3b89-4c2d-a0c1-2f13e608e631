#!/bin/bash

# 检查是否已经在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "⚠️  检测到已激活的虚拟环境: $(basename $VIRTUAL_ENV)"
    echo "是否要切换到项目虚拟环境? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        deactivate
    else
        echo "保持当前环境"
        exit 0
    fi
fi

# 激活项目虚拟环境
source /Users/<USER>/PycharmProjects/PythonProject1/venv/bin/activate

echo "✅ 虚拟环境已激活: venv"
echo ""
echo "可用命令:"
echo "  python data_manager_cli.py download -s AAPL"
echo "  python strategy_with_local_data.py"
echo "  python demo_with_sample_data.py"
echo ""
echo "要退出虚拟环境，输入: deactivate"

# 启动新的 bash 会话，但保持在当前目录
exec bash --rcfile <(echo "PS1='(venv) \u@\h:\w\$ '")
