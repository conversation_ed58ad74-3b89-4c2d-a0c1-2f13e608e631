#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
虚拟环境管理脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def check_environments():
    """检查项目中的虚拟环境"""
    print("=" * 50)
    print("虚拟环境检查")
    print("=" * 50)
    
    project_root = Path.cwd()
    
    # 检查可能的虚拟环境目录
    env_dirs = ['.venv', 'venv', 'env', '.env']
    found_envs = []
    
    for env_dir in env_dirs:
        env_path = project_root / env_dir
        if env_path.exists() and env_path.is_dir():
            # 检查是否是虚拟环境
            if (env_path / 'bin' / 'python').exists() or (env_path / 'Scripts' / 'python.exe').exists():
                found_envs.append(env_path)
    
    if not found_envs:
        print("❌ 未找到虚拟环境")
        return None
    
    print(f"找到 {len(found_envs)} 个虚拟环境:")
    
    for i, env_path in enumerate(found_envs, 1):
        print(f"\n{i}. {env_path.name}")
        print(f"   路径: {env_path}")
        
        # 检查 Python 版本
        try:
            if os.name == 'nt':  # Windows
                python_exe = env_path / 'Scripts' / 'python.exe'
            else:  # Unix/Linux/macOS
                python_exe = env_path / 'bin' / 'python'
            
            if python_exe.exists():
                result = subprocess.run([str(python_exe), '--version'], 
                                      capture_output=True, text=True)
                print(f"   Python 版本: {result.stdout.strip()}")
        except:
            print("   Python 版本: 无法获取")
        
        # 检查已安装的包
        try:
            if os.name == 'nt':
                pip_exe = env_path / 'Scripts' / 'pip'
            else:
                pip_exe = env_path / 'bin' / 'pip'
            
            if pip_exe.exists():
                result = subprocess.run([str(pip_exe), 'list', '--format=freeze'], 
                                      capture_output=True, text=True)
                packages = result.stdout.strip().split('\n')
                key_packages = ['pandas', 'numpy', 'matplotlib', 'backtrader', 'yfinance', 'tushare']
                installed_key_packages = [pkg for pkg in packages if any(key in pkg.lower() for key in key_packages)]
                
                print(f"   已安装包数量: {len(packages)}")
                if installed_key_packages:
                    print(f"   关键包: {', '.join(installed_key_packages[:5])}")
                    if len(installed_key_packages) > 5:
                        print(f"           ... 还有 {len(installed_key_packages) - 5} 个")
                else:
                    print("   关键包: 无")
        except:
            print("   包信息: 无法获取")
    
    return found_envs


def recommend_environment(envs):
    """推荐使用的环境"""
    if not envs:
        return None
    
    print(f"\n" + "=" * 50)
    print("环境推荐")
    print("=" * 50)
    
    # 评分系统
    scores = []
    
    for env_path in envs:
        score = 0
        details = []
        
        try:
            # 检查包数量
            if os.name == 'nt':
                pip_exe = env_path / 'Scripts' / 'pip'
            else:
                pip_exe = env_path / 'bin' / 'pip'
            
            if pip_exe.exists():
                result = subprocess.run([str(pip_exe), 'list', '--format=freeze'], 
                                      capture_output=True, text=True)
                packages = result.stdout.strip().split('\n')
                
                # 包数量评分
                if len(packages) > 50:
                    score += 30
                    details.append("包数量丰富")
                elif len(packages) > 10:
                    score += 20
                    details.append("包数量适中")
                else:
                    score += 5
                    details.append("包数量较少")
                
                # 关键包评分
                key_packages = ['pandas', 'numpy', 'matplotlib', 'backtrader', 'yfinance']
                for key_pkg in key_packages:
                    if any(key_pkg.lower() in pkg.lower() for pkg in packages):
                        score += 10
                        details.append(f"包含 {key_pkg}")
        except:
            score += 1
            details.append("无法检查包")
        
        # 名称偏好
        if env_path.name == 'venv':
            score += 5
            details.append("标准名称")
        elif env_path.name == '.venv':
            score += 3
            details.append("隐藏环境")
        
        scores.append((score, env_path, details))
    
    # 排序
    scores.sort(key=lambda x: x[0], reverse=True)
    
    print("环境评分 (分数越高越推荐):")
    for i, (score, env_path, details) in enumerate(scores, 1):
        print(f"\n{i}. {env_path.name} (评分: {score})")
        print(f"   路径: {env_path}")
        print(f"   特点: {', '.join(details)}")
    
    recommended = scores[0][1]
    print(f"\n🎯 推荐使用: {recommended.name}")
    print(f"   路径: {recommended}")
    
    return recommended


def create_activation_script(recommended_env):
    """创建激活脚本"""
    if not recommended_env:
        return
    
    print(f"\n" + "=" * 50)
    print("创建便捷脚本")
    print("=" * 50)
    
    # 创建激活脚本
    if os.name == 'nt':  # Windows
        script_content = f"""@echo off
call {recommended_env}\\Scripts\\activate.bat
echo 虚拟环境已激活: {recommended_env.name}
echo 可用命令:
echo   python data_manager_cli.py download -s AAPL
echo   python strategy_with_local_data.py
echo   python demo_with_sample_data.py
cmd /k
"""
        script_name = "activate_env.bat"
    else:  # Unix/Linux/macOS
        script_content = f"""#!/bin/bash
source {recommended_env}/bin/activate
echo "虚拟环境已激活: {recommended_env.name}"
echo "可用命令:"
echo "  python data_manager_cli.py download -s AAPL"
echo "  python strategy_with_local_data.py"
echo "  python demo_with_sample_data.py"
echo ""
echo "要退出虚拟环境，输入: deactivate"
exec bash
"""
        script_name = "activate_env.sh"
    
    with open(script_name, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    if os.name != 'nt':
        os.chmod(script_name, 0o755)
    
    print(f"✅ 创建激活脚本: {script_name}")
    
    # 创建运行脚本
    run_scripts = {
        'run_download.py': f'''#!/usr/bin/env python
import subprocess
import sys
import os

# 激活虚拟环境并运行命令
if os.name == 'nt':
    activate_script = r"{recommended_env}\\Scripts\\activate.bat"
    cmd = f'"{activate_script}" && python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01'
    subprocess.run(cmd, shell=True)
else:
    activate_script = f"source {recommended_env}/bin/activate"
    cmd = f'{activate_script} && python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01'
    subprocess.run(cmd, shell=True, executable='/bin/bash')
''',
        
        'run_strategy.py': f'''#!/usr/bin/env python
import subprocess
import sys
import os

# 激活虚拟环境并运行命令
if os.name == 'nt':
    activate_script = r"{recommended_env}\\Scripts\\activate.bat"
    cmd = f'"{activate_script}" && python strategy_with_local_data.py'
    subprocess.run(cmd, shell=True)
else:
    activate_script = f"source {recommended_env}/bin/activate"
    cmd = f'{activate_script} && python strategy_with_local_data.py'
    subprocess.run(cmd, shell=True, executable='/bin/bash')
''',
        
        'run_demo.py': f'''#!/usr/bin/env python
import subprocess
import sys
import os

# 激活虚拟环境并运行命令
if os.name == 'nt':
    activate_script = r"{recommended_env}\\Scripts\\activate.bat"
    cmd = f'"{activate_script}" && python demo_with_sample_data.py'
    subprocess.run(cmd, shell=True)
else:
    activate_script = f"source {recommended_env}/bin/activate"
    cmd = f'{activate_script} && python demo_with_sample_data.py'
    subprocess.run(cmd, shell=True, executable='/bin/bash')
'''
    }
    
    for script_name, content in run_scripts.items():
        with open(script_name, 'w', encoding='utf-8') as f:
            f.write(content)
        if os.name != 'nt':
            os.chmod(script_name, 0o755)
        print(f"✅ 创建运行脚本: {script_name}")


def clean_unused_environments(envs, recommended_env):
    """清理未使用的环境"""
    if len(envs) <= 1:
        return
    
    print(f"\n" + "=" * 50)
    print("环境清理建议")
    print("=" * 50)
    
    unused_envs = [env for env in envs if env != recommended_env]
    
    if unused_envs:
        print("发现以下未推荐的环境:")
        for env in unused_envs:
            print(f"  - {env}")
        
        print(f"\n建议:")
        print(f"1. 保留推荐环境: {recommended_env.name}")
        print(f"2. 可以删除其他环境以节省空间")
        print(f"3. 删除命令: rm -rf {' '.join(str(env) for env in unused_envs)}")
        print(f"\n⚠️  删除前请确认不再需要这些环境！")


def main():
    """主函数"""
    print("虚拟环境管理工具")
    
    # 检查环境
    envs = check_environments()
    
    if not envs:
        print("\n建议创建虚拟环境:")
        print("  python -m venv venv")
        print("  source venv/bin/activate  # Linux/macOS")
        print("  venv\\Scripts\\activate     # Windows")
        return
    
    # 推荐环境
    recommended = recommend_environment(envs)
    
    # 创建脚本
    create_activation_script(recommended)
    
    # 清理建议
    clean_unused_environments(envs, recommended)
    
    print(f"\n" + "=" * 50)
    print("使用建议")
    print("=" * 50)
    print(f"1. 使用推荐环境: {recommended.name}")
    print(f"2. 激活命令:")
    if os.name == 'nt':
        print(f"   {recommended}\\Scripts\\activate.bat")
    else:
        print(f"   source {recommended}/bin/activate")
    print(f"3. 或直接运行:")
    print(f"   python run_download.py")
    print(f"   python run_strategy.py")
    print(f"   python run_demo.py")


if __name__ == '__main__':
    main()
