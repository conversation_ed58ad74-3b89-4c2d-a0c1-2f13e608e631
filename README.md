# 双线均值回归策略 - Backtrader实现

这是一个使用Backtrader框架实现的双线均值回归交易策略，通过快速移动平均线和慢速移动平均线的交叉来判断买入卖出时机。

## 策略原理

### 基本逻辑
1. **买入信号**: 当快速移动平均线上穿慢速移动平均线时买入
2. **卖出信号**: 当快速移动平均线下穿慢速移动平均线时卖出
3. **均值回归**: 利用价格向移动平均线回归的特性获利

### 策略特点
- 简单易懂的技术指标策略
- 适合趋势性较强的市场
- 可自定义移动平均线周期
- 支持止损止盈功能

## 文件说明

### 核心文件
- `dual_ma_strategy.py` - 主策略实现文件
- `run_strategy.py` - 简单运行示例
- `custom_strategy_example.py` - 自定义策略和参数比较

### 策略参数
- `fast_period`: 快线周期 (默认: 10)
- `slow_period`: 慢线周期 (默认: 30)
- `stop_loss`: 止损比例 (自定义策略中使用)
- `take_profit`: 止盈比例 (自定义策略中使用)

## 安装依赖

首先创建虚拟环境并安装必要的包：

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install backtrader matplotlib yfinance pandas
```

## 使用方法

### 1. 基本使用

运行基本策略示例：

```bash
python dual_ma_strategy.py
```

这将对苹果公司(AAPL)股票进行2023年的回测。

### 2. 多股票回测

运行多个股票的回测示例：

```bash
python run_strategy.py
```

这将依次回测AAPL、MSFT、TSLA三只股票。

### 3. 自定义策略

运行带止损止盈的自定义策略：

```bash
python custom_strategy_example.py
```

这将运行增强版策略并比较不同参数组合的效果。

### 4. 自定义参数

你可以在代码中修改以下参数：

```python
# 在dual_ma_strategy.py中修改
run_backtest(
    symbol='AAPL',           # 股票代码
    start_date='2023-01-01', # 开始日期
    end_date='2024-01-01',   # 结束日期
    initial_cash=10000,      # 初始资金
    commission=0.001         # 手续费率
)
```

## 策略分析

### 输出指标
- **总收益率**: 策略的总体收益表现
- **夏普比率**: 风险调整后的收益率
- **最大回撤**: 策略的最大亏损幅度
- **胜率**: 盈利交易占总交易的比例
- **交易次数**: 策略执行的总交易数量

### 可视化图表
策略运行后会自动生成包含以下内容的图表：
- 股价K线图
- 快速和慢速移动平均线
- 买入卖出信号点
- 资金曲线

## 策略优化建议

### 1. 参数优化
- 尝试不同的移动平均线周期组合
- 根据不同股票的特性调整参数
- 考虑市场波动性调整止损止盈比例

### 2. 风险控制
- 设置合理的止损水平
- 控制单次交易的资金比例
- 考虑加入仓位管理

### 3. 市场适应性
- 在不同市场环境下测试策略
- 考虑加入市场趋势过滤器
- 结合其他技术指标增强信号

## 注意事项

1. **历史回测不代表未来表现**: 策略的历史表现不能保证未来收益
2. **交易成本**: 实际交易中需要考虑手续费、滑点等成本
3. **市场环境**: 策略在不同市场环境下表现可能差异很大
4. **风险管理**: 请根据自身风险承受能力调整策略参数

## 扩展功能

### 可以添加的功能
- 多时间框架分析
- 动态止损跟踪
- 资金管理模块
- 实时交易接口
- 更多技术指标组合

### 数据源扩展
- 支持更多股票市场
- 加入加密货币数据
- 期货和外汇数据支持

## 联系和支持

如果你在使用过程中遇到问题或有改进建议，欢迎提出issue或贡献代码。

---

**免责声明**: 本策略仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
