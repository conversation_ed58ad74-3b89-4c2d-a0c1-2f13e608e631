#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地数据管理器 - 支持 Tushare 和 yfinance 双数据源
"""

import sqlite3
import pandas as pd
import numpy as np
import yfinance as yf
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入 tushare
try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
    logger.info("Tushare 可用")
except ImportError:
    TUSHARE_AVAILABLE = False
    logger.warning("Tushare 未安装，只能使用 yfinance")


class LocalDataManager:
    """本地数据管理器"""
    
    def __init__(self, db_path="stock_data.db", tushare_token=None):
        """
        初始化数据管理器
        
        Args:
            db_path: 数据库文件路径
            tushare_token: Tushare API token
        """
        self.db_path = db_path
        self.tushare_token = tushare_token
        
        # 初始化 Tushare
        if TUSHARE_AVAILABLE and tushare_token:
            try:
                ts.set_token(tushare_token)
                self.ts_pro = ts.pro_api()
                self.tushare_enabled = True
                logger.info("Tushare API 初始化成功")
            except Exception as e:
                logger.warning(f"Tushare API 初始化失败: {e}")
                self.tushare_enabled = False
        else:
            self.tushare_enabled = False
        
        # 初始化数据库
        self._init_database()
        
        # 数据源优先级：tushare > yfinance
        self.data_sources = []
        if self.tushare_enabled:
            self.data_sources.append('tushare')
        # self.data_sources.append('yfinance')
        
        logger.info(f"可用数据源: {self.data_sources}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建股票基本信息表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_info (
                    symbol TEXT PRIMARY KEY,
                    name TEXT,
                    market TEXT,
                    industry TEXT,
                    list_date TEXT,
                    data_source TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume INTEGER,
                    amount REAL,
                    adj_close REAL,
                    data_source TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, trade_date)
                )
            ''')
            
            # 创建数据更新记录表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS update_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    data_type TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    data_source TEXT,
                    status TEXT,
                    error_msg TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_daily_symbol_date ON daily_data(symbol, trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_daily_date ON daily_data(trade_date)')
            
            conn.commit()
            logger.info("数据库初始化完成")
    
    def _fetch_from_tushare(self, symbol, start_date, end_date):
        """从 Tushare 获取数据"""
        if not self.tushare_enabled:
            return None
        
        try:
            # 转换股票代码格式
            ts_symbol = self._convert_symbol_to_tushare(symbol)
            if not ts_symbol:
                return None
            
            logger.info(f"从 Tushare 获取 {ts_symbol} 数据...")
            
            # 获取日线数据
            df = self.ts_pro.daily(
                ts_code=ts_symbol,
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', '')
            )
            
            if df.empty:
                return None
            
            # 数据格式转换
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            df.set_index('trade_date', inplace=True)
            
            # 重命名列以符合标准格式
            df = df.rename(columns={
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'vol': 'volume',
                'amount': 'amount'
            })
            
            # 添加调整后收盘价（Tushare 的 close 已经是复权价）
            df['adj_close'] = df['close']
            
            # 选择需要的列
            df = df[['open', 'high', 'low', 'close', 'volume', 'amount', 'adj_close']]
            
            logger.info(f"Tushare 获取到 {len(df)} 条数据")
            return df
            
        except Exception as e:
            logger.error(f"Tushare 获取数据失败: {e}")
            return None
    
    def _fetch_from_yfinance(self, symbol, start_date, end_date):
        """从 yfinance 获取数据"""
        try:
            logger.info(f"从 yfinance 获取 {symbol} 数据...")
            
            # 添加延迟避免限流
            time.sleep(1)
            
            ticker = yf.Ticker(symbol)
            df = ticker.history(
                start=start_date,
                end=end_date,
                auto_adjust=False,  # 不自动调整，保留原始价格
                prepost=False
            )
            
            if df.empty:
                return None
            
            # 重命名列
            df.columns = [col.lower() for col in df.columns]
            
            # 添加成交额（估算）
            df['amount'] = df['close'] * df['volume']
            
            # 重命名 adj close
            if 'adj close' in df.columns:
                df['adj_close'] = df['adj close']
                df = df.drop('adj close', axis=1)
            else:
                df['adj_close'] = df['close']
            
            # 选择需要的列
            df = df[['open', 'high', 'low', 'close', 'volume', 'amount', 'adj_close']]
            
            logger.info(f"yfinance 获取到 {len(df)} 条数据")
            return df
            
        except Exception as e:
            logger.error(f"yfinance 获取数据失败: {e}")
            return None
    
    def _convert_symbol_to_tushare(self, symbol):
        """将股票代码转换为 Tushare 格式"""
        symbol = symbol.upper()
        
        # 美股代码直接返回 None，Tushare 主要支持 A 股
        if symbol in ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN', 'META', 'NVDA']:
            return None
        
        # A股代码转换
        if symbol.isdigit() and len(symbol) == 6:
            if symbol.startswith('6'):
                return f"{symbol}.SH"  # 上交所
            elif symbol.startswith(('0', '3')):
                return f"{symbol}.SZ"  # 深交所
        
        # 港股代码
        if symbol.isdigit() and len(symbol) <= 5:
            return f"{symbol.zfill(5)}.HK"
        
        return None
    
    def fetch_and_store_data(self, symbol, start_date, end_date, force_update=False):
        """获取并存储数据"""
        # 检查本地是否已有数据
        if not force_update:
            local_data = self.get_local_data(symbol, start_date, end_date)
            if local_data is not None and not local_data.empty:
                logger.info(f"{symbol} 本地数据已存在，跳过下载")
                return local_data
        
        # 尝试从各个数据源获取数据
        data = None
        used_source = None
        
        for source in self.data_sources:
            try:
                if source == 'tushare':
                    data = self._fetch_from_tushare(symbol, start_date, end_date)
                elif source == 'yfinance':
                    data = self._fetch_from_yfinance(symbol, start_date, end_date)
                
                if data is not None and not data.empty:
                    used_source = source
                    break
                    
            except Exception as e:
                logger.warning(f"从 {source} 获取 {symbol} 数据失败: {e}")
                continue
        
        if data is None or data.empty:
            logger.error(f"所有数据源都无法获取 {symbol} 的数据")
            self._log_update(symbol, 'daily', start_date, end_date, 'failed', 'all_sources_failed')
            return None
        
        # 存储到数据库
        try:
            self._store_daily_data(symbol, data, used_source)
            self._log_update(symbol, 'daily', start_date, end_date, used_source, 'success')
            logger.info(f"成功存储 {symbol} 数据到本地数据库")
            return data
            
        except Exception as e:
            logger.error(f"存储数据失败: {e}")
            self._log_update(symbol, 'daily', start_date, end_date, used_source, f'storage_failed: {e}')
            return data  # 即使存储失败，也返回数据供使用
    
    def _store_daily_data(self, symbol, data, data_source):
        """存储日线数据到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            for date, row in data.iterrows():
                conn.execute('''
                    INSERT OR REPLACE INTO daily_data 
                    (symbol, trade_date, open, high, low, close, volume, amount, adj_close, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    date.strftime('%Y-%m-%d'),
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    int(row['volume']) if not pd.isna(row['volume']) else 0,
                    float(row['amount']) if not pd.isna(row['amount']) else 0,
                    float(row['adj_close']),
                    data_source
                ))
            conn.commit()
    
    def _log_update(self, symbol, data_type, start_date, end_date, data_source, status, error_msg=None):
        """记录更新日志"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT INTO update_log 
                (symbol, data_type, start_date, end_date, data_source, status, error_msg)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, data_type, start_date, end_date, data_source, status, error_msg))
            conn.commit()
    
    def get_local_data(self, symbol, start_date, end_date):
        """从本地数据库获取数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT trade_date, open, high, low, close, volume, amount, adj_close
                    FROM daily_data 
                    WHERE symbol = ? AND trade_date >= ? AND trade_date <= ?
                    ORDER BY trade_date
                '''
                
                df = pd.read_sql_query(query, conn, params=(symbol, start_date, end_date))
                
                if df.empty:
                    return None
                
                # 设置日期索引
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df.set_index('trade_date', inplace=True)
                
                logger.info(f"从本地数据库获取到 {symbol} {len(df)} 条数据")
                return df
                
        except Exception as e:
            logger.error(f"从本地数据库获取数据失败: {e}")
            return None
    
    def get_data(self, symbol, start_date, end_date, force_update=False):
        """获取数据的主接口"""
        # 首先尝试从本地获取
        if not force_update:
            local_data = self.get_local_data(symbol, start_date, end_date)
            if local_data is not None and not local_data.empty:
                # 检查数据完整性
                expected_days = pd.date_range(start=start_date, end=end_date, freq='D')
                expected_trading_days = expected_days[expected_days.weekday < 5]  # 工作日
                
                coverage = len(local_data) / len(expected_trading_days)
                if coverage > 0.8:  # 如果覆盖率超过80%，认为数据足够
                    logger.info(f"使用本地数据，覆盖率: {coverage:.1%}")
                    return local_data
        
        # 本地数据不足，从网络获取
        return self.fetch_and_store_data(symbol, start_date, end_date, force_update)
    
    def update_symbol_list(self, symbols):
        """批量更新股票列表"""
        logger.info(f"开始批量更新 {len(symbols)} 个股票的数据...")
        
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')  # 最近一年
        
        success_count = 0
        for i, symbol in enumerate(symbols):
            logger.info(f"更新进度: {i+1}/{len(symbols)} - {symbol}")
            
            try:
                data = self.fetch_and_store_data(symbol, start_date, end_date)
                if data is not None:
                    success_count += 1
                
                # 添加延迟避免限流
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"更新 {symbol} 失败: {e}")
        
        logger.info(f"批量更新完成: {success_count}/{len(symbols)} 成功")
        return success_count
    
    def get_database_info(self):
        """获取数据库信息"""
        with sqlite3.connect(self.db_path) as conn:
            # 统计信息
            cursor = conn.cursor()
            
            # 股票数量
            cursor.execute("SELECT COUNT(DISTINCT symbol) FROM daily_data")
            symbol_count = cursor.fetchone()[0]
            
            # 数据记录数
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            record_count = cursor.fetchone()[0]
            
            # 数据日期范围
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data")
            date_range = cursor.fetchone()
            
            # 数据源统计
            cursor.execute("SELECT data_source, COUNT(*) FROM daily_data GROUP BY data_source")
            source_stats = cursor.fetchall()
            
            return {
                'symbol_count': symbol_count,
                'record_count': record_count,
                'date_range': date_range,
                'source_stats': dict(source_stats),
                'db_size': os.path.getsize(self.db_path) / 1024 / 1024  # MB
            }


if __name__ == '__main__':
    # 测试数据管理器
    manager = LocalDataManager()
    
    # 获取数据库信息
    info = manager.get_database_info()
    print("数据库信息:")
    print(f"  股票数量: {info['symbol_count']}")
    print(f"  数据记录: {info['record_count']}")
    print(f"  数据范围: {info['date_range']}")
    print(f"  数据源统计: {info['source_stats']}")
    print(f"  数据库大小: {info['db_size']:.2f} MB")
    
    # 测试获取数据
    data = manager.get_data('AAPL', '2023-01-01', '2023-12-31')
    if data is not None:
        print(f"\n获取到 AAPL 数据: {len(data)} 条")
        print(data.head())
