#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终策略测试 - 使用高级数据获取器
"""

import backtrader as bt
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from advanced_data_fetcher import AdvancedDataFetcher
from dual_ma_strategy import DualMAStrategy


def run_advanced_backtest(symbol='AAPL', start_date='2023-01-01', end_date='2024-01-01', 
                         initial_cash=10000, commission=0.001):
    """使用高级数据获取器运行回测"""
    
    print(f"=" * 60)
    print(f"高级回测: {symbol}")
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"初始资金: ${initial_cash:,.2f}")
    print(f"手续费率: {commission*100:.3f}%")
    print("=" * 60)
    
    # 1. 获取数据
    print("1. 获取股票数据...")
    fetcher = AdvancedDataFetcher()
    data = fetcher.get_stock_data(symbol, start_date, end_date)
    
    if data is None or data.empty:
        print("❌ 无法获取数据")
        return None
    
    print(f"✅ 成功获取数据: {len(data)} 条记录")
    print(f"   数据范围: {data.index[0].date()} 到 {data.index[-1].date()}")
    
    # 2. 数据质量检查
    print("\n2. 数据质量检查...")
    
    # 检查缺失值
    missing_data = data.isnull().sum()
    if missing_data.sum() > 0:
        print(f"⚠️  发现缺失数据: {missing_data.to_dict()}")
        data = data.dropna()
        print(f"   清理后数据: {len(data)} 条记录")
    else:
        print("✅ 数据完整，无缺失值")
    
    # 检查价格合理性
    if 'close' in data.columns:
        price_stats = data['close'].describe()
        print(f"   价格统计: 最低${price_stats['min']:.2f}, "
              f"最高${price_stats['max']:.2f}, "
              f"平均${price_stats['mean']:.2f}")
        
        # 检查异常价格
        if price_stats['min'] <= 0:
            print("❌ 发现异常价格数据")
            return None
        
        print("✅ 价格数据合理")
    
    # 3. 设置回测引擎
    print("\n3. 设置回测引擎...")
    cerebro = bt.Cerebro()
    
    # 添加策略
    cerebro.addstrategy(DualMAStrategy,
                       fast_period=10,
                       slow_period=30,
                       printlog=True)
    
    # 添加数据
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # 设置资金和手续费
    cerebro.broker.setcash(initial_cash)
    cerebro.broker.setcommission(commission=commission)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
    
    # 4. 运行回测
    print("\n4. 运行回测...")
    print(f"初始资金: ${cerebro.broker.getvalue():,.2f}")
    
    results = cerebro.run()
    
    final_value = cerebro.broker.getvalue()
    total_return = final_value - initial_cash
    return_pct = (total_return / initial_cash) * 100
    
    print(f"最终资金: ${final_value:,.2f}")
    print(f"总收益: ${total_return:,.2f}")
    print(f"收益率: {return_pct:.2f}%")
    
    # 5. 分析结果
    print("\n5. 详细分析结果")
    print("=" * 40)
    
    strat = results[0]
    
    # 夏普比率
    try:
        sharpe = strat.analyzers.sharpe.get_analysis()
        if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:
            print(f"夏普比率: {sharpe['sharperatio']:.3f}")
        else:
            print("夏普比率: 无法计算")
    except:
        print("夏普比率: 计算出错")
    
    # 最大回撤
    try:
        drawdown = strat.analyzers.drawdown.get_analysis()
        max_dd = drawdown.get('max', {}).get('drawdown', 0)
        print(f"最大回撤: {max_dd:.2f}%")
    except:
        print("最大回撤: 计算出错")
    
    # 交易统计
    try:
        trades = strat.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)
        
        print(f"总交易次数: {total_trades}")
        print(f"盈利交易: {won_trades}")
        print(f"亏损交易: {lost_trades}")
        
        if total_trades > 0:
            win_rate = (won_trades / total_trades) * 100
            print(f"胜率: {win_rate:.2f}%")
            
            # 平均盈亏
            if won_trades > 0:
                avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
                print(f"平均盈利: ${avg_win:.2f}")
            
            if lost_trades > 0:
                avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
                print(f"平均亏损: ${avg_loss:.2f}")
    except:
        print("交易统计: 计算出错")
    
    # SQN (System Quality Number)
    try:
        sqn = strat.analyzers.sqn.get_analysis()
        if 'sqn' in sqn:
            print(f"系统质量数(SQN): {sqn['sqn']:.2f}")
    except:
        print("SQN: 计算出错")
    
    # 6. 绘制图表
    print("\n6. 生成图表...")
    try:
        cerebro.plot(style='candlestick', barup='green', bardown='red')
        plt.show()
        print("✅ 图表已显示")
    except Exception as e:
        print(f"⚠️  图表生成失败: {e}")
    
    return results


def compare_strategies():
    """比较不同策略参数"""
    print("\n" + "=" * 60)
    print("策略参数比较")
    print("=" * 60)
    
    # 测试不同的参数组合
    param_sets = [
        {'fast': 5, 'slow': 15, 'name': '激进策略(5/15)'},
        {'fast': 10, 'slow': 30, 'name': '标准策略(10/30)'},
        {'fast': 20, 'slow': 50, 'name': '保守策略(20/50)'},
    ]
    
    # 获取数据
    fetcher = AdvancedDataFetcher()
    data = fetcher.get_stock_data('AAPL', '2023-06-01', '2023-12-31')
    
    if data is None or data.empty:
        print("❌ 无法获取数据进行比较")
        return
    
    results = []
    
    for params in param_sets:
        print(f"\n测试 {params['name']}...")
        
        cerebro = bt.Cerebro()
        cerebro.addstrategy(DualMAStrategy,
                           fast_period=params['fast'],
                           slow_period=params['slow'],
                           printlog=False)
        
        data_feed = bt.feeds.PandasData(dataname=data)
        cerebro.adddata(data_feed)
        cerebro.broker.setcash(10000)
        cerebro.broker.setcommission(commission=0.001)
        
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        initial_value = cerebro.broker.getvalue()
        result = cerebro.run()
        final_value = cerebro.broker.getvalue()
        
        strat = result[0]
        
        # 计算指标
        total_return = (final_value - initial_value) / initial_value * 100
        drawdown = strat.analyzers.drawdown.get_analysis()
        max_dd = drawdown.get('max', {}).get('drawdown', 0)
        
        trades = strat.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        win_rate = 0
        if total_trades > 0:
            won_trades = trades.get('won', {}).get('total', 0)
            win_rate = (won_trades / total_trades) * 100
        
        results.append({
            'name': params['name'],
            'return': total_return,
            'drawdown': max_dd,
            'trades': total_trades,
            'win_rate': win_rate
        })
        
        print(f"  收益率: {total_return:.2f}%")
        print(f"  最大回撤: {max_dd:.2f}%")
        print(f"  交易次数: {total_trades}")
        print(f"  胜率: {win_rate:.2f}%")
    
    # 总结
    print("\n" + "=" * 40)
    print("比较总结")
    print("=" * 40)
    
    best_return = max(results, key=lambda x: x['return'])
    best_drawdown = min(results, key=lambda x: x['drawdown'])
    best_winrate = max(results, key=lambda x: x['win_rate'])
    
    print(f"最佳收益: {best_return['name']} ({best_return['return']:.2f}%)")
    print(f"最小回撤: {best_drawdown['name']} ({best_drawdown['drawdown']:.2f}%)")
    print(f"最高胜率: {best_winrate['name']} ({best_winrate['win_rate']:.2f}%)")


if __name__ == '__main__':
    print("最终策略测试程序")
    
    try:
        # 1. 运行单个策略回测
        print("\n1. 运行AAPL策略回测...")
        result = run_advanced_backtest(
            symbol='AAPL',
            start_date='2023-06-01',
            end_date='2023-12-31',
            initial_cash=10000,
            commission=0.001
        )
        
        if result:
            print("✅ 策略回测完成")
            
            # 2. 策略参数比较
            print("\n2. 进行策略参数比较...")
            compare_strategies()
            
        else:
            print("❌ 策略回测失败")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n测试完成！")
