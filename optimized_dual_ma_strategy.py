#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的双线均值回归策略 - 添加风险控制和信号过滤
"""

import backtrader as bt
import numpy as np


class OptimizedDualMAStrategy(bt.Strategy):
    """
    优化的双线均值回归策略
    
    优化要点：
    1. 添加止损止盈机制
    2. 趋势强度过滤
    3. 动态仓位管理
    4. 信号确认机制
    """
    
    params = (
        # 移动平均线参数
        ('fast_period', 10),
        ('slow_period', 30),
        
        # 风险控制参数
        ('stop_loss', 0.08),        # 8% 止损
        ('take_profit', 0.15),      # 15% 止盈
        ('trailing_stop', 0.05),    # 5% 跟踪止损
        
        # 仓位管理
        ('position_size', 0.8),     # 80% 仓位
        ('max_risk_per_trade', 0.02), # 单笔最大风险2%
        
        # 信号过滤
        ('min_trend_strength', 0.02), # 最小趋势强度2%
        ('signal_confirmation', 2),    # 信号确认天数
        ('volume_filter', True),       # 成交量过滤
        
        # 其他
        ('printlog', True),
    )
    
    def __init__(self):
        """初始化策略"""
        # 移动平均线
        self.fast_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.fast_period
        )
        self.slow_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.slow_period
        )
        
        # 技术指标
        self.crossover = bt.indicators.CrossOver(self.fast_ma, self.slow_ma)
        self.rsi = bt.indicators.RSI(self.data.close, period=14)
        self.atr = bt.indicators.ATR(self.data, period=14)
        
        # 成交量指标
        self.volume_ma = bt.indicators.SimpleMovingAverage(
            self.data.volume, period=20
        )
        
        # 状态变量
        self.order = None
        self.buyprice = None
        self.buycomm = None
        self.stop_price = None
        self.profit_price = None
        self.signal_count = 0
        
    def log(self, txt, dt=None):
        """日志记录"""
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}: {txt}')
    
    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入执行: 价格 {order.executed.price:.2f}, '
                        f'数量 {order.executed.size}, '
                        f'手续费 {order.executed.comm:.2f}')
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                
                # 设置止损止盈价格
                self.stop_price = self.buyprice * (1 - self.params.stop_loss)
                self.profit_price = self.buyprice * (1 + self.params.take_profit)
                
                self.log(f'设置止损: {self.stop_price:.2f}, 止盈: {self.profit_price:.2f}')
                
            else:
                self.log(f'卖出执行: 价格 {order.executed.price:.2f}, '
                        f'数量 {order.executed.size}, '
                        f'手续费 {order.executed.comm:.2f}')
                
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单取消/保证金不足/拒绝')
        
        self.order = None
    
    def notify_trade(self, trade):
        """交易完成通知"""
        if not trade.isclosed:
            return
        
        self.log(f'交易盈亏: 毛利润 {trade.pnl:.2f}, 净利润 {trade.pnlcomm:.2f}')
    
    def calculate_position_size(self):
        """动态计算仓位大小"""
        # 基于ATR的仓位计算
        if len(self.atr) > 0:
            risk_amount = self.broker.getcash() * self.params.max_risk_per_trade
            atr_value = self.atr[0]
            stop_distance = self.data.close[0] * self.params.stop_loss
            
            if stop_distance > 0:
                size = int(risk_amount / stop_distance)
                max_size = int(self.broker.getcash() * self.params.position_size / self.data.close[0])
                return min(size, max_size)
        
        # 默认仓位计算
        return int(self.broker.getcash() * self.params.position_size / self.data.close[0])
    
    def check_trend_strength(self):
        """检查趋势强度"""
        if len(self.fast_ma) < 2 or len(self.slow_ma) < 2:
            return False
        
        # 计算MA斜率
        fast_slope = (self.fast_ma[0] - self.fast_ma[-1]) / self.fast_ma[-1]
        slow_slope = (self.slow_ma[0] - self.slow_ma[-1]) / self.slow_ma[-1]
        
        # 趋势强度过滤
        return abs(fast_slope) > self.params.min_trend_strength or \
               abs(slow_slope) > self.params.min_trend_strength
    
    def check_volume_filter(self):
        """成交量过滤"""
        if not self.params.volume_filter:
            return True
        
        if len(self.volume_ma) > 0:
            return self.data.volume[0] > self.volume_ma[0] * 1.2  # 成交量放大20%
        
        return True
    
    def check_rsi_filter(self, is_buy_signal):
        """RSI过滤"""
        if len(self.rsi) == 0:
            return True
        
        rsi_value = self.rsi[0]
        
        if is_buy_signal:
            return rsi_value < 70  # 避免超买时买入
        else:
            return rsi_value > 30  # 避免超卖时卖出
    
    def update_trailing_stop(self):
        """更新跟踪止损"""
        if self.position and self.buyprice:
            current_price = self.data.close[0]
            
            # 计算新的跟踪止损价格
            trailing_stop_price = current_price * (1 - self.params.trailing_stop)
            
            # 只有当新止损价格更高时才更新
            if trailing_stop_price > self.stop_price:
                self.stop_price = trailing_stop_price
                self.log(f'更新跟踪止损: {self.stop_price:.2f}')
    
    def next(self):
        """策略主逻辑"""
        # 记录当前状态
        self.log(f'收盘价: {self.data.close[0]:.2f}, '
                f'快线: {self.fast_ma[0]:.2f}, '
                f'慢线: {self.slow_ma[0]:.2f}, '
                f'RSI: {self.rsi[0]:.1f}')
        
        # 如果有未完成订单，等待
        if self.order:
            return
        
        # 更新跟踪止损
        if self.position:
            self.update_trailing_stop()
        
        # 检查是否已持仓
        if not self.position:
            # 没有持仓，检查买入信号
            if self.crossover > 0:  # 快线上穿慢线
                # 信号确认计数
                self.signal_count += 1
                
                if self.signal_count >= self.params.signal_confirmation:
                    # 多重过滤条件
                    trend_ok = self.check_trend_strength()
                    volume_ok = self.check_volume_filter()
                    rsi_ok = self.check_rsi_filter(True)
                    
                    if trend_ok and volume_ok and rsi_ok:
                        size = self.calculate_position_size()
                        if size > 0:
                            self.log(f'买入信号确认: 趋势强度✓, 成交量✓, RSI✓')
                            self.order = self.buy(size=size)
                            self.signal_count = 0
                    else:
                        self.log(f'买入信号被过滤: 趋势{trend_ok}, 成交量{volume_ok}, RSI{rsi_ok}')
            else:
                self.signal_count = 0
                
        else:
            # 已持仓，检查卖出条件
            current_price = self.data.close[0]
            sell_signal = False
            sell_reason = ""
            
            # 止损检查
            if current_price <= self.stop_price:
                sell_signal = True
                sell_reason = f"止损触发 (价格: {current_price:.2f} <= 止损: {self.stop_price:.2f})"
            
            # 止盈检查
            elif current_price >= self.profit_price:
                sell_signal = True
                sell_reason = f"止盈触发 (价格: {current_price:.2f} >= 止盈: {self.profit_price:.2f})"
            
            # 技术信号检查
            elif self.crossover < 0:  # 快线下穿慢线
                # RSI过滤
                if self.check_rsi_filter(False):
                    sell_signal = True
                    sell_reason = "技术信号: 快线下穿慢线"
                else:
                    self.log("卖出信号被RSI过滤")
            
            if sell_signal:
                self.log(f'卖出信号: {sell_reason}')
                self.order = self.sell(size=self.position.size)
                # 重置价格
                self.stop_price = None
                self.profit_price = None


def run_optimized_backtest(symbol='300059', start_date='2020-01-01', end_date='2025-01-01'):
    """运行优化策略回测"""
    from local_data_manager import LocalDataManager
    from data_config import config
    
    print(f"=" * 60)
    print(f"优化策略回测: {symbol}")
    print("=" * 60)
    
    # 获取数据
    manager = LocalDataManager(
        db_path=config.get_database_path(),
        tushare_token=config.get_tushare_token()
    )
    
    data = manager.get_data(symbol, start_date, end_date)
    if data is None or data.empty:
        print("❌ 无法获取数据")
        return None
    
    print(f"✅ 数据: {len(data)} 条记录")
    
    # 创建回测引擎
    cerebro = bt.Cerebro()
    
    # 添加优化策略
    cerebro.addstrategy(OptimizedDualMAStrategy,
                       fast_period=10,
                       slow_period=30,
                       stop_loss=0.08,
                       take_profit=0.15,
                       position_size=0.8,
                       printlog=True)
    
    # 添加数据
    data_feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(data_feed)
    
    # 设置初始资金和手续费
    cerebro.broker.setcash(10000)
    cerebro.broker.setcommission(commission=0.001)
    
    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    
    # 运行回测
    print(f'初始资金: ${cerebro.broker.getvalue():,.2f}')
    results = cerebro.run()
    final_value = cerebro.broker.getvalue()
    
    print(f'最终资金: ${final_value:,.2f}')
    print(f'总收益: ${final_value - 10000:,.2f}')
    print(f'收益率: {((final_value - 10000) / 10000) * 100:.2f}%')
    
    # 分析结果
    strat = results[0]
    
    # 夏普比率
    try:
        sharpe = strat.analyzers.sharpe.get_analysis()
        if 'sharperatio' in sharpe and sharpe['sharperatio'] is not None:
            print(f"夏普比率: {sharpe['sharperatio']:.3f}")
    except:
        print("夏普比率: 计算出错")
    
    # 最大回撤
    try:
        drawdown = strat.analyzers.drawdown.get_analysis()
        max_dd = drawdown.get('max', {}).get('drawdown', 0)
        print(f"最大回撤: {max_dd:.2f}%")
    except:
        print("最大回撤: 计算出错")
    
    # 交易统计
    try:
        trades = strat.analyzers.trades.get_analysis()
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)
        
        print(f"总交易次数: {total_trades}")
        print(f"盈利交易: {won_trades}")
        print(f"亏损交易: {lost_trades}")
        
        if total_trades > 0:
            win_rate = (won_trades / total_trades) * 100
            print(f"胜率: {win_rate:.2f}%")
            
            if won_trades > 0:
                avg_win = trades.get('won', {}).get('pnl', {}).get('average', 0)
                print(f"平均盈利: ${avg_win:.2f}")
            
            if lost_trades > 0:
                avg_loss = trades.get('lost', {}).get('pnl', {}).get('average', 0)
                print(f"平均亏损: ${avg_loss:.2f}")
                
                if avg_loss != 0:
                    profit_loss_ratio = abs(avg_win / avg_loss) if avg_win > 0 else 0
                    print(f"盈亏比: {profit_loss_ratio:.2f}")
    except:
        print("交易统计: 计算出错")
    
    return results


if __name__ == '__main__':
    # 运行优化策略回测
    run_optimized_backtest('300059', '2020-01-01', '2025-01-01')
