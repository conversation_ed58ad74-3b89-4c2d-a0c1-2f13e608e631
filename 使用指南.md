
# 本地数据库股票回测系统使用指南

## 系统组件

1. **local_data_manager.py** - 核心数据管理器
2. **data_config.py** - 配置管理
3. **data_manager_cli.py** - 命令行工具
4. **strategy_with_local_data.py** - 策略回测

## 快速开始

### 1. 配置数据源
```bash
python data_config.py
```

### 2. 下载数据
```bash
# 下载单个股票
python data_manager_cli.py download -s AAPL --start-date 2023-01-01

# 下载多个股票
python data_manager_cli.py download -s AAPL,MSFT,GOOGL --start-date 2023-01-01
```

### 3. 查询数据
```bash
# 查询数据
python data_manager_cli.py query AAPL --start-date 2023-01-01

# 导出数据
python data_manager_cli.py query AAPL -o aapl_data.csv
```

### 4. 运行回测
```bash
python strategy_with_local_data.py
```

## 数据源配置

### Tushare (A股数据)
1. 注册 Tushare 账号: https://tushare.pro/
2. 获取 API Token
3. 运行配置向导设置 Token

### yfinance (美股数据)
- 默认启用，无需额外配置
- 支持美股、港股等市场

## 命令行工具

### 下载数据
```bash
python data_manager_cli.py download -s AAPL,MSFT --start-date 2023-01-01 --end-date 2023-12-31
```

### 查看数据库信息
```bash
python data_manager_cli.py info --list-symbols
```

### 批量更新
```bash
python data_manager_cli.py update --symbols AAPL,MSFT,GOOGL
```

## 策略回测

### 单个股票回测
```python
from strategy_with_local_data import run_backtest_with_local_data

result = run_backtest_with_local_data(
    symbol='AAPL',
    start_date='2023-01-01',
    end_date='2023-12-31',
    initial_cash=10000
)
```

### 批量回测
```python
from strategy_with_local_data import batch_backtest

results = batch_backtest(['AAPL', 'MSFT', 'GOOGL'])
```

## 数据库结构

- **stock_info**: 股票基本信息
- **daily_data**: 日线数据
- **update_log**: 更新日志

## 注意事项

1. 首次使用需要下载数据，可能需要较长时间
2. 建议定期更新数据以保持最新
3. Tushare 有访问频率限制，请合理使用
4. 数据库文件会随着数据增加而增大

## 故障排除

### 数据下载失败
- 检查网络连接
- 检查 API Token 是否正确
- 查看错误日志

### 回测失败
- 确保数据已下载
- 检查日期范围是否合理
- 查看数据质量

## 扩展功能

- 支持自定义策略
- 支持多种技术指标
- 支持参数优化
- 支持风险管理

更多信息请参考源代码注释。
