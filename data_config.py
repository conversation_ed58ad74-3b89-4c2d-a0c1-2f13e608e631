#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据源配置文件
"""

import os
import json
from pathlib import Path

class DataConfig:
    """数据源配置管理"""
    
    def __init__(self, config_file="data_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        default_config = {
            "database": {
                "path": "stock_data.db",
                "backup_enabled": True,
                "backup_interval_days": 7
            },
            "tushare": {
                "enabled": False,
                "token": "",
                "rate_limit": {
                    "requests_per_minute": 200,
                    "daily_limit": 10000
                }
            },
            "yfinance": {
                "enabled": True,
                "rate_limit": {
                    "requests_per_minute": 60,
                    "delay_between_requests": 1
                }
            },
            "data_sources": {
                "priority": ["tushare", "yfinance"],
                "fallback_enabled": True
            },
            "update_schedule": {
                "auto_update": False,
                "update_time": "09:00",
                "update_symbols": ["AAPL", "MSFT", "GOOGL", "TSLA"]
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # 合并配置，确保所有默认键都存在
                return self._merge_config(default_config, loaded_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
                return default_config
        else:
            # 创建默认配置文件
            self._save_config(default_config)
            return default_config
    
    def _merge_config(self, default, loaded):
        """合并配置，确保所有默认键都存在"""
        for key, value in default.items():
            if key not in loaded:
                loaded[key] = value
            elif isinstance(value, dict) and isinstance(loaded[key], dict):
                loaded[key] = self._merge_config(value, loaded[key])
        return loaded
    
    def _save_config(self, config=None):
        """保存配置文件"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key_path, default=None):
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        self._save_config()
    
    def setup_tushare(self, token):
        """设置 Tushare 配置"""
        self.set('tushare.token', token)
        self.set('tushare.enabled', True)
        print("Tushare 配置已更新")
    
    def disable_tushare(self):
        """禁用 Tushare"""
        self.set('tushare.enabled', False)
        print("Tushare 已禁用")
    
    def get_database_path(self):
        """获取数据库路径"""
        return self.get('database.path', 'stock_data.db')
    
    def get_tushare_token(self):
        """获取 Tushare token"""
        if self.get('tushare.enabled', False):
            return self.get('tushare.token', '16984276bbf64b31a286ec6aa0a8d09a3032ce5bc7dc443022c4faef')
        return None
    
    def is_tushare_enabled(self):
        """检查 Tushare 是否启用"""
        return self.get('tushare.enabled', False) and bool(self.get('tushare.token', ''))
    
    def is_yfinance_enabled(self):
        """检查 yfinance 是否启用"""
        return self.get('yfinance.enabled', True)
    
    def get_data_source_priority(self):
        """获取数据源优先级"""
        return self.get('data_sources.priority', ['yfinance'])


# 全局配置实例
config = DataConfig()


def setup_wizard():
    """配置向导"""
    print("=" * 50)
    print("数据源配置向导")
    print("=" * 50)
    
    # Tushare 配置
    print("\n1. Tushare 配置 (主要用于A股数据)")
    use_tushare = input("是否使用 Tushare? (y/n): ").lower().strip()
    
    if use_tushare == 'y':
        token = input("请输入 Tushare Token: ").strip()
        if token:
            config.setup_tushare(token)
        else:
            print("Token 为空，跳过 Tushare 配置")
    else:
        config.disable_tushare()
    
    # yfinance 配置
    print("\n2. yfinance 配置 (主要用于美股数据)")
    use_yfinance = input("是否使用 yfinance? (y/n, 默认 y): ").lower().strip()
    
    if use_yfinance != 'n':
        config.set('yfinance.enabled', True)
        print("yfinance 已启用")
    else:
        config.set('yfinance.enabled', False)
        print("yfinance 已禁用")
    
    # 数据库路径
    print("\n3. 数据库配置")
    db_path = input(f"数据库路径 (默认: {config.get_database_path()}): ").strip()
    if db_path:
        config.set('database.path', db_path)
    
    # 更新股票列表
    print("\n4. 默认股票列表")
    current_symbols = config.get('update_schedule.update_symbols', [])
    print(f"当前股票列表: {current_symbols}")
    
    new_symbols = input("输入新的股票列表 (用逗号分隔，回车跳过): ").strip()
    if new_symbols:
        symbols = [s.strip().upper() for s in new_symbols.split(',')]
        config.set('update_schedule.update_symbols', symbols)
        print(f"股票列表已更新: {symbols}")
    
    print("\n配置完成！")
    print(f"配置文件已保存到: {config.config_file}")


if __name__ == '__main__':
    setup_wizard()
