#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管理命令行工具
"""

import argparse
import sys
from datetime import datetime, timedelta
from local_data_manager import LocalDataManager
from data_config import config
import pandas as pd


def init_data_manager():
    """初始化数据管理器"""
    db_path = config.get_database_path()
    tushare_token = config.get_tushare_token()
    
    return LocalDataManager(db_path=db_path, tushare_token=tushare_token)


def download_data(args):
    """下载数据命令"""
    manager = init_data_manager()
    
    symbols = args.symbols.split(',') if args.symbols else ['AAPL']
    start_date = args.start_date or (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    end_date = args.end_date or datetime.now().strftime('%Y-%m-%d')
    
    print(f"开始下载数据...")
    print(f"股票代码: {symbols}")
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"强制更新: {args.force}")
    
    success_count = 0
    for symbol in symbols:
        print(f"\n下载 {symbol} 数据...")
        try:
            data = manager.fetch_and_store_data(
                symbol=symbol.strip().upper(),
                start_date=start_date,
                end_date=end_date,
                force_update=args.force
            )
            
            if data is not None and not data.empty:
                print(f"✅ {symbol}: {len(data)} 条记录")
                success_count += 1
            else:
                print(f"❌ {symbol}: 下载失败")
                
        except Exception as e:
            print(f"❌ {symbol}: 错误 - {e}")
    
    print(f"\n下载完成: {success_count}/{len(symbols)} 成功")


def query_data(args):
    """查询数据命令"""
    manager = init_data_manager()
    
    symbol = args.symbol.upper()
    start_date = args.start_date or (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    end_date = args.end_date or datetime.now().strftime('%Y-%m-%d')
    
    print(f"查询 {symbol} 数据 ({start_date} 到 {end_date})...")
    
    data = manager.get_data(symbol, start_date, end_date)
    
    if data is not None and not data.empty:
        print(f"找到 {len(data)} 条记录")
        
        if args.output:
            # 保存到文件
            if args.output.endswith('.csv'):
                data.to_csv(args.output)
                print(f"数据已保存到: {args.output}")
            elif args.output.endswith('.xlsx'):
                data.to_excel(args.output)
                print(f"数据已保存到: {args.output}")
            else:
                print("不支持的文件格式，请使用 .csv 或 .xlsx")
        else:
            # 显示数据摘要
            print("\n数据摘要:")
            print(f"  时间范围: {data.index[0].date()} 到 {data.index[-1].date()}")
            print(f"  收盘价范围: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
            print(f"  平均成交量: {data['volume'].mean():,.0f}")
            
            print("\n最近5天数据:")
            print(data.tail().round(2))
    else:
        print("未找到数据")


def database_info(args):
    """数据库信息命令"""
    manager = init_data_manager()
    
    info = manager.get_database_info()
    
    print("数据库信息:")
    print("=" * 40)
    print(f"数据库路径: {manager.db_path}")
    print(f"数据库大小: {info['db_size']:.2f} MB")
    print(f"股票数量: {info['symbol_count']}")
    print(f"数据记录: {info['record_count']:,}")
    
    if info['date_range'][0] and info['date_range'][1]:
        print(f"数据范围: {info['date_range'][0]} 到 {info['date_range'][1]}")
    
    if info['source_stats']:
        print("\n数据源统计:")
        for source, count in info['source_stats'].items():
            print(f"  {source}: {count:,} 条记录")
    
    # 显示股票列表
    if args.list_symbols:
        import sqlite3
        with sqlite3.connect(manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT symbol FROM daily_data ORDER BY symbol")
            symbols = [row[0] for row in cursor.fetchall()]
            
            print(f"\n股票列表 ({len(symbols)} 个):")
            for i, symbol in enumerate(symbols):
                if i % 10 == 0:
                    print()
                print(f"{symbol:8}", end="")
            print()


def update_symbols(args):
    """批量更新股票数据"""
    manager = init_data_manager()
    
    if args.symbols:
        symbols = [s.strip().upper() for s in args.symbols.split(',')]
    else:
        symbols = config.get('update_schedule.update_symbols', ['AAPL', 'MSFT', 'GOOGL'])
    
    print(f"批量更新 {len(symbols)} 个股票...")
    success_count = manager.update_symbol_list(symbols)
    print(f"更新完成: {success_count}/{len(symbols)} 成功")


def setup_config(args):
    """配置设置"""
    from data_config import setup_wizard
    setup_wizard()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票数据管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 下载数据命令
    download_parser = subparsers.add_parser('download', help='下载股票数据')
    download_parser.add_argument('--symbols', '-s', required=True, 
                               help='股票代码，多个用逗号分隔 (如: AAPL,MSFT,GOOGL)')
    download_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    download_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    download_parser.add_argument('--force', '-f', action='store_true', help='强制更新')
    download_parser.set_defaults(func=download_data)
    
    # 查询数据命令
    query_parser = subparsers.add_parser('query', help='查询股票数据')
    query_parser.add_argument('symbol', help='股票代码')
    query_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    query_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    query_parser.add_argument('--output', '-o', help='输出文件路径 (.csv 或 .xlsx)')
    query_parser.set_defaults(func=query_data)
    
    # 数据库信息命令
    info_parser = subparsers.add_parser('info', help='显示数据库信息')
    info_parser.add_argument('--list-symbols', '-l', action='store_true', help='列出所有股票代码')
    info_parser.set_defaults(func=database_info)
    
    # 批量更新命令
    update_parser = subparsers.add_parser('update', help='批量更新股票数据')
    update_parser.add_argument('--symbols', '-s', help='股票代码，多个用逗号分隔')
    update_parser.set_defaults(func=update_symbols)
    
    # 配置命令
    config_parser = subparsers.add_parser('config', help='配置数据源')
    config_parser.set_defaults(func=setup_config)
    
    # 解析参数
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        return
    
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"错误: {e}")
        if args.command == 'config':
            print("请检查配置文件或重新运行配置向导")


if __name__ == '__main__':
    main()
