#!/bin/bash

# 简单的虚拟环境激活脚本
# 使用方法: source activate_simple.sh

# 检查是否已经在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "⚠️  当前已在虚拟环境中: $(basename $VIRTUAL_ENV)"
    echo "先退出当前环境..."
    deactivate
fi

# 激活项目虚拟环境
echo "🔄 激活项目虚拟环境..."
source venv/bin/activate

if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 虚拟环境已激活: $(basename $VIRTUAL_ENV)"
    echo ""
    echo "📋 可用命令:"
    echo "  python data_manager_cli.py download -s AAPL"
    echo "  python data_manager_cli.py query AAPL"
    echo "  python strategy_with_local_data.py"
    echo "  python demo_with_sample_data.py"
    echo ""
    echo "💡 要退出虚拟环境，输入: deactivate"
else
    echo "❌ 虚拟环境激活失败"
fi
